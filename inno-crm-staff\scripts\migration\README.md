# Data Migration Guide

This directory contains scripts to migrate data from the existing monolithic CRM system to the new multi-server architecture (Staff and Students portals).

## Overview

The migration process consists of three main steps:

1. **Export** - Extract data from the monolithic system
2. **Migrate** - Import data into the new Staff and Students databases
3. **Validate** - Verify data integrity and consistency

## Prerequisites

- Access to the existing monolithic CRM database
- Both Staff and Students servers running and accessible
- Proper environment variables configured
- Database connections established

## Migration Scripts

### 1. Export Script (`export-monolith-data.ts`)

Extracts data from the monolithic system and saves it as JSON files.

```bash
npm run migrate:export
```

**What it does:**
- Connects to the monolithic database
- Exports students, groups, teachers, payments, attendance, and assessments
- Saves data to `migration-data/` directory
- Creates an export summary with statistics

**Output files:**
- `students.json` - Student records
- `groups.json` - Group/class information
- `teachers.json` - Teacher profiles
- `payments.json` - Payment records
- `attendance.json` - Attendance records
- `assessments.json` - Assessment/test results
- `users.json` - User accounts
- `courses.json` - Course definitions
- `export-summary.json` - Export statistics

### 2. Migration Script (`migrate-from-monolith.ts`)

Imports the exported data into the new Staff and Students databases.

```bash
npm run migrate:from-monolith
```

**What it does:**
- Reads exported JSON files from `migration-data/`
- Creates records in the Staff database
- Syncs reference data to the Students database via API
- Maintains data relationships and integrity
- Logs all operations to `migration-log.txt`

**Migration order:**
1. Teachers (required for groups)
2. Groups (required for student assignments)
3. Students (creates full records and references)
4. Payments (financial data)

### 3. Validation Script (`validate-migration.ts`)

Verifies the migration was successful and data is consistent.

```bash
npm run migrate:validate
```

**What it does:**
- Compares exported data with migrated data
- Checks record counts and data integrity
- Validates inter-server communication
- Tests API endpoints
- Generates a validation report

**Validation checks:**
- Record count verification
- Data field consistency
- Cross-server reference integrity
- API communication health
- Missing or orphaned records

## Full Migration Process

Run the complete migration process:

```bash
npm run migrate:full
```

This executes all three scripts in sequence and stops if any step fails.

## Manual Migration Steps

### Step 1: Prepare Environment

1. Ensure both databases are running:
   ```bash
   # Staff database
   npm run db:push
   
   # Students database (in students project)
   cd ../inno-crm-students
   npm run db:push
   ```

2. Start both servers:
   ```bash
   # Staff server
   npm run dev
   
   # Students server (in students project)
   cd ../inno-crm-students
   npm run dev
   ```

3. Verify environment variables:
   ```bash
   # Staff project .env.local
   DATABASE_URL="postgresql://..."
   STUDENTS_SERVER_URL="http://localhost:3002"
   STUDENTS_API_KEY="your-api-key"
   STUDENTS_SECRET_KEY="your-secret-key"
   ```

### Step 2: Export Data

1. Connect to the monolithic system
2. Run the export script:
   ```bash
   npm run migrate:export
   ```
3. Verify export files in `migration-data/`

### Step 3: Run Migration

1. Review exported data for any issues
2. Run the migration:
   ```bash
   npm run migrate:from-monolith
   ```
3. Monitor the migration log for errors

### Step 4: Validate Results

1. Run validation:
   ```bash
   npm run migrate:validate
   ```
2. Review the validation report
3. Fix any issues found

## Troubleshooting

### Common Issues

1. **Database Connection Errors**
   - Verify database URLs in environment variables
   - Ensure databases are running and accessible
   - Check network connectivity

2. **API Communication Failures**
   - Verify both servers are running
   - Check API keys and secret keys
   - Ensure correct server URLs

3. **Data Validation Failures**
   - Review migration logs for specific errors
   - Check for missing foreign key relationships
   - Verify data format consistency

4. **Memory Issues with Large Datasets**
   - Process data in smaller batches
   - Increase Node.js memory limit: `--max-old-space-size=4096`
   - Consider streaming for very large datasets

### Recovery Procedures

1. **Rollback Migration**
   ```bash
   # Reset Staff database
   npm run db:push -- --force-reset
   
   # Reset Students database
   cd ../inno-crm-students
   npm run db:push -- --force-reset
   ```

2. **Partial Re-migration**
   - Edit migration scripts to skip already migrated data
   - Use `upsert` operations instead of `create`
   - Filter by date ranges or specific IDs

3. **Data Cleanup**
   ```bash
   # Remove migration data
   rm -rf migration-data/
   
   # Clear logs
   rm migration-log.txt
   ```

## Performance Considerations

### Large Datasets

For systems with large amounts of data (>10,000 records):

1. **Batch Processing**
   - Process records in batches of 100-500
   - Add delays between batches to prevent overload

2. **Parallel Processing**
   - Run independent migrations in parallel
   - Use worker threads for CPU-intensive operations

3. **Database Optimization**
   - Disable foreign key checks during migration
   - Use bulk insert operations
   - Create indexes after migration

### Monitoring

- Monitor database performance during migration
- Watch memory usage and connection counts
- Set up alerts for long-running operations

## Security Considerations

1. **Data Protection**
   - Encrypt exported data files
   - Use secure channels for data transfer
   - Implement access controls

2. **API Security**
   - Use strong API keys and secrets
   - Implement rate limiting
   - Monitor for unauthorized access

3. **Audit Trail**
   - Log all migration operations
   - Track data changes and transformations
   - Maintain backup of original data

## Post-Migration Tasks

1. **Verify Application Functionality**
   - Test all major features
   - Verify user authentication
   - Check data relationships

2. **Performance Testing**
   - Run load tests on both systems
   - Monitor query performance
   - Optimize slow queries

3. **User Training**
   - Train staff on new system
   - Update documentation
   - Provide support during transition

4. **Monitoring Setup**
   - Configure application monitoring
   - Set up database monitoring
   - Implement alerting

## Support

For migration support:
- Review logs in `migration-log.txt`
- Check validation report in `migration-data/validation-report.json`
- Contact development team for assistance
- Refer to system documentation for troubleshooting
