#!/usr/bin/env tsx

/**
 * Export script to extract data from monolithic CRM database
 * This script should be run on the existing monolithic system
 * 
 * Usage: npm run export:monolith-data
 */

import fs from 'fs/promises'
import path from 'path'

// Mock Prisma client for the monolithic system
// In real implementation, this would be the actual Prisma client from the monolithic system
interface MonolithPrismaClient {
  student: {
    findMany: () => Promise<any[]>
  }
  group: {
    findMany: (options?: any) => Promise<any[]>
  }
  teacher: {
    findMany: (options?: any) => Promise<any[]>
  }
  payment: {
    findMany: () => Promise<any[]>
  }
  user: {
    findMany: () => Promise<any[]>
  }
  course: {
    findMany: () => Promise<any[]>
  }
  attendance: {
    findMany: () => Promise<any[]>
  }
  assessment: {
    findMany: () => Promise<any[]>
  }
}

class MonolithDataExporter {
  private prisma: MonolithPrismaClient
  private exportDir: string

  constructor() {
    // In real implementation, initialize the actual Prisma client
    this.prisma = this.createMockPrismaClient()
    this.exportDir = path.join(process.cwd(), 'migration-data')
  }

  private createMockPrismaClient(): MonolithPrismaClient {
    // Mock implementation - replace with actual Prisma client
    return {
      student: {
        findMany: async () => [
          {
            id: 'student-1',
            name: 'John Doe',
            phone: '+998901234567',
            email: '<EMAIL>',
            level: 'B1',
            status: 'ACTIVE',
            branch: 'main',
            emergencyContact: '+998901234568',
            dateOfBirth: '1995-01-15',
            address: 'Tashkent, Uzbekistan',
            currentGroupId: 'group-1',
            createdAt: '2024-01-01T00:00:00.000Z',
            updatedAt: '2024-01-01T00:00:00.000Z'
          },
          {
            id: 'student-2',
            name: 'Jane Smith',
            phone: '+998901234569',
            email: '<EMAIL>',
            level: 'A2',
            status: 'ACTIVE',
            branch: 'main',
            emergencyContact: '+998901234570',
            dateOfBirth: '1998-03-22',
            address: 'Tashkent, Uzbekistan',
            currentGroupId: 'group-2',
            createdAt: '2024-01-02T00:00:00.000Z',
            updatedAt: '2024-01-02T00:00:00.000Z'
          }
        ]
      },
      group: {
        findMany: async (options) => [
          {
            id: 'group-1',
            name: 'B1 Morning Group',
            teacherId: 'teacher-1',
            courseId: 'course-1',
            courseName: 'General English B1',
            schedule: {
              days: ['MON', 'WED', 'FRI'],
              time: '09:00',
              duration: 90
            },
            room: 'Room 101',
            branch: 'main',
            startDate: '2024-01-01T00:00:00.000Z',
            endDate: '2024-06-01T00:00:00.000Z',
            isActive: true,
            createdAt: '2024-01-01T00:00:00.000Z',
            updatedAt: '2024-01-01T00:00:00.000Z',
            teacher: options?.include?.teacher ? {
              id: 'teacher-1',
              name: 'Alice Johnson',
              user: { name: 'Alice Johnson' }
            } : undefined,
            course: options?.include?.course ? {
              id: 'course-1',
              name: 'General English B1'
            } : undefined
          },
          {
            id: 'group-2',
            name: 'A2 Evening Group',
            teacherId: 'teacher-2',
            courseId: 'course-2',
            courseName: 'General English A2',
            schedule: {
              days: ['TUE', 'THU'],
              time: '18:00',
              duration: 90
            },
            room: 'Room 102',
            branch: 'main',
            startDate: '2024-01-15T00:00:00.000Z',
            endDate: '2024-06-15T00:00:00.000Z',
            isActive: true,
            createdAt: '2024-01-15T00:00:00.000Z',
            updatedAt: '2024-01-15T00:00:00.000Z',
            teacher: options?.include?.teacher ? {
              id: 'teacher-2',
              name: 'Bob Wilson',
              user: { name: 'Bob Wilson' }
            } : undefined,
            course: options?.include?.course ? {
              id: 'course-2',
              name: 'General English A2'
            } : undefined
          }
        ]
      },
      teacher: {
        findMany: async (options) => [
          {
            id: 'teacher-1',
            userId: 'user-1',
            name: 'Alice Johnson',
            subject: 'English',
            branch: 'main',
            photoUrl: '/images/teachers/alice.jpg',
            createdAt: '2024-01-01T00:00:00.000Z',
            updatedAt: '2024-01-01T00:00:00.000Z',
            user: options?.include?.user ? {
              id: 'user-1',
              name: 'Alice Johnson',
              email: '<EMAIL>'
            } : undefined
          },
          {
            id: 'teacher-2',
            userId: 'user-2',
            name: 'Bob Wilson',
            subject: 'English',
            branch: 'main',
            photoUrl: '/images/teachers/bob.jpg',
            createdAt: '2024-01-01T00:00:00.000Z',
            updatedAt: '2024-01-01T00:00:00.000Z',
            user: options?.include?.user ? {
              id: 'user-2',
              name: 'Bob Wilson',
              email: '<EMAIL>'
            } : undefined
          }
        ]
      },
      payment: {
        findMany: async () => [
          {
            id: 'payment-1',
            studentId: 'student-1',
            amount: 500000,
            method: 'CASH',
            status: 'PAID',
            paidDate: '2024-01-15T00:00:00.000Z',
            dueDate: '2024-01-31T00:00:00.000Z',
            description: 'Monthly tuition fee',
            createdAt: '2024-01-01T00:00:00.000Z',
            updatedAt: '2024-01-15T00:00:00.000Z'
          },
          {
            id: 'payment-2',
            studentId: 'student-2',
            amount: 450000,
            method: 'CARD',
            status: 'PAID',
            paidDate: '2024-01-20T00:00:00.000Z',
            dueDate: '2024-01-31T00:00:00.000Z',
            description: 'Monthly tuition fee',
            createdAt: '2024-01-02T00:00:00.000Z',
            updatedAt: '2024-01-20T00:00:00.000Z'
          }
        ]
      },
      user: {
        findMany: async () => [
          {
            id: 'user-1',
            name: 'Alice Johnson',
            email: '<EMAIL>',
            role: 'TEACHER',
            createdAt: '2024-01-01T00:00:00.000Z',
            updatedAt: '2024-01-01T00:00:00.000Z'
          },
          {
            id: 'user-2',
            name: 'Bob Wilson',
            email: '<EMAIL>',
            role: 'TEACHER',
            createdAt: '2024-01-01T00:00:00.000Z',
            updatedAt: '2024-01-01T00:00:00.000Z'
          }
        ]
      },
      course: {
        findMany: async () => [
          {
            id: 'course-1',
            name: 'General English B1',
            description: 'Intermediate English course',
            level: 'B1',
            duration: 120,
            createdAt: '2024-01-01T00:00:00.000Z',
            updatedAt: '2024-01-01T00:00:00.000Z'
          },
          {
            id: 'course-2',
            name: 'General English A2',
            description: 'Pre-intermediate English course',
            level: 'A2',
            duration: 100,
            createdAt: '2024-01-01T00:00:00.000Z',
            updatedAt: '2024-01-01T00:00:00.000Z'
          }
        ]
      },
      attendance: {
        findMany: async () => [
          {
            id: 'attendance-1',
            studentId: 'student-1',
            groupId: 'group-1',
            status: 'PRESENT',
            date: '2024-01-15T09:00:00.000Z',
            notes: null,
            createdAt: '2024-01-15T09:00:00.000Z',
            updatedAt: '2024-01-15T09:00:00.000Z'
          },
          {
            id: 'attendance-2',
            studentId: 'student-2',
            groupId: 'group-2',
            status: 'PRESENT',
            date: '2024-01-16T18:00:00.000Z',
            notes: null,
            createdAt: '2024-01-16T18:00:00.000Z',
            updatedAt: '2024-01-16T18:00:00.000Z'
          }
        ]
      },
      assessment: {
        findMany: async () => [
          {
            id: 'assessment-1',
            studentId: 'student-1',
            groupId: 'group-1',
            type: 'QUIZ',
            title: 'Unit 1 Quiz',
            score: 85,
            maxScore: 100,
            completedAt: '2024-01-20T10:00:00.000Z',
            createdAt: '2024-01-15T00:00:00.000Z',
            updatedAt: '2024-01-20T10:00:00.000Z'
          },
          {
            id: 'assessment-2',
            studentId: 'student-2',
            groupId: 'group-2',
            type: 'HOMEWORK',
            title: 'Grammar Exercise 1',
            score: 92,
            maxScore: 100,
            completedAt: '2024-01-18T20:00:00.000Z',
            createdAt: '2024-01-16T00:00:00.000Z',
            updatedAt: '2024-01-18T20:00:00.000Z'
          }
        ]
      }
    }
  }

  async ensureExportDirectory() {
    try {
      await fs.access(this.exportDir)
    } catch {
      await fs.mkdir(this.exportDir, { recursive: true })
    }
  }

  async exportStudents() {
    console.log('Exporting students...')
    const students = await this.prisma.student.findMany()
    
    // Transform data for the new schema
    const transformedStudents = students.map(student => ({
      id: student.id,
      name: student.name,
      phone: student.phone,
      email: student.email,
      level: student.level,
      status: student.status,
      branch: student.branch,
      emergencyContact: student.emergencyContact,
      dateOfBirth: student.dateOfBirth,
      address: student.address,
      currentGroupId: student.currentGroupId,
      createdAt: student.createdAt,
      updatedAt: student.updatedAt
    }))

    await fs.writeFile(
      path.join(this.exportDir, 'students.json'),
      JSON.stringify(transformedStudents, null, 2)
    )
    
    console.log(`Exported ${transformedStudents.length} students`)
    return transformedStudents.length
  }

  async exportGroups() {
    console.log('Exporting groups...')
    const groups = await this.prisma.group.findMany({
      include: {
        teacher: { include: { user: true } },
        course: true
      }
    })
    
    // Transform data for the new schema
    const transformedGroups = groups.map(group => ({
      id: group.id,
      name: group.name,
      teacherId: group.teacherId,
      courseId: group.courseId,
      courseName: group.course?.name || group.courseName,
      schedule: group.schedule,
      room: group.room,
      branch: group.branch,
      startDate: group.startDate,
      endDate: group.endDate,
      isActive: group.isActive,
      createdAt: group.createdAt,
      updatedAt: group.updatedAt
    }))

    await fs.writeFile(
      path.join(this.exportDir, 'groups.json'),
      JSON.stringify(transformedGroups, null, 2)
    )
    
    console.log(`Exported ${transformedGroups.length} groups`)
    return transformedGroups.length
  }

  async exportTeachers() {
    console.log('Exporting teachers...')
    const teachers = await this.prisma.teacher.findMany({
      include: { user: true }
    })
    
    // Transform data for the new schema
    const transformedTeachers = teachers.map(teacher => ({
      id: teacher.id,
      userId: teacher.userId,
      name: teacher.user?.name || teacher.name,
      subject: teacher.subject,
      branch: teacher.branch,
      photoUrl: teacher.photoUrl,
      createdAt: teacher.createdAt,
      updatedAt: teacher.updatedAt
    }))

    await fs.writeFile(
      path.join(this.exportDir, 'teachers.json'),
      JSON.stringify(transformedTeachers, null, 2)
    )
    
    console.log(`Exported ${transformedTeachers.length} teachers`)
    return transformedTeachers.length
  }

  async exportPayments() {
    console.log('Exporting payments...')
    const payments = await this.prisma.payment.findMany()
    
    // Transform data for the new schema
    const transformedPayments = payments.map(payment => ({
      id: payment.id,
      studentId: payment.studentId,
      amount: payment.amount,
      method: payment.method,
      status: payment.status,
      paidDate: payment.paidDate,
      dueDate: payment.dueDate,
      description: payment.description,
      createdAt: payment.createdAt,
      updatedAt: payment.updatedAt
    }))

    await fs.writeFile(
      path.join(this.exportDir, 'payments.json'),
      JSON.stringify(transformedPayments, null, 2)
    )
    
    console.log(`Exported ${transformedPayments.length} payments`)
    return transformedPayments.length
  }

  async exportAttendance() {
    console.log('Exporting attendance records...')
    const attendance = await this.prisma.attendance.findMany()
    
    await fs.writeFile(
      path.join(this.exportDir, 'attendance.json'),
      JSON.stringify(attendance, null, 2)
    )
    
    console.log(`Exported ${attendance.length} attendance records`)
    return attendance.length
  }

  async exportAssessments() {
    console.log('Exporting assessments...')
    const assessments = await this.prisma.assessment.findMany()
    
    await fs.writeFile(
      path.join(this.exportDir, 'assessments.json'),
      JSON.stringify(assessments, null, 2)
    )
    
    console.log(`Exported ${assessments.length} assessments`)
    return assessments.length
  }

  async exportUsers() {
    console.log('Exporting users...')
    const users = await this.prisma.user.findMany()
    
    await fs.writeFile(
      path.join(this.exportDir, 'users.json'),
      JSON.stringify(users, null, 2)
    )
    
    console.log(`Exported ${users.length} users`)
    return users.length
  }

  async exportCourses() {
    console.log('Exporting courses...')
    const courses = await this.prisma.course.findMany()
    
    await fs.writeFile(
      path.join(this.exportDir, 'courses.json'),
      JSON.stringify(courses, null, 2)
    )
    
    console.log(`Exported ${courses.length} courses`)
    return courses.length
  }

  async exportAll() {
    const startTime = Date.now()
    
    console.log('Starting data export from monolithic system...')
    
    await this.ensureExportDirectory()
    
    const results = {
      students: await this.exportStudents(),
      groups: await this.exportGroups(),
      teachers: await this.exportTeachers(),
      payments: await this.exportPayments(),
      attendance: await this.exportAttendance(),
      assessments: await this.exportAssessments(),
      users: await this.exportUsers(),
      courses: await this.exportCourses()
    }
    
    const duration = Date.now() - startTime
    
    // Create export summary
    const summary = {
      exportDate: new Date().toISOString(),
      duration,
      results,
      totalRecords: Object.values(results).reduce((sum, count) => sum + count, 0)
    }
    
    await fs.writeFile(
      path.join(this.exportDir, 'export-summary.json'),
      JSON.stringify(summary, null, 2)
    )
    
    console.log('\n=== Export Summary ===')
    Object.entries(results).forEach(([table, count]) => {
      console.log(`${table}: ${count} records`)
    })
    console.log(`\nTotal: ${summary.totalRecords} records`)
    console.log(`Duration: ${duration}ms`)
    console.log(`Export directory: ${this.exportDir}`)
    
    return summary
  }
}

// Main execution
async function main() {
  const exporter = new MonolithDataExporter()
  
  try {
    await exporter.exportAll()
    console.log('\nData export completed successfully!')
    console.log('You can now run the migration script to import this data into the new system.')
  } catch (error) {
    console.error('Export failed:', error)
    process.exit(1)
  }
}

if (require.main === module) {
  main()
}
