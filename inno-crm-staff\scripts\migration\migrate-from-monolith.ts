#!/usr/bin/env tsx

/**
 * Migration script to migrate data from monolithic CRM to separate Staff and Students databases
 * 
 * Usage: npm run migrate:from-monolith
 */

import { PrismaClient } from '@prisma/client'
import { getStudentsClient } from '../../lib/api-clients/students-client'
import fs from 'fs/promises'
import path from 'path'

interface MonolithStudent {
  id: string
  name: string
  phone: string
  email?: string
  level: string
  status: string
  branch: string
  emergencyContact?: string
  dateOfBirth?: string
  address?: string
  currentGroupId?: string
  createdAt: string
  updatedAt: string
}

interface MonolithGroup {
  id: string
  name: string
  teacherId: string
  courseId: string
  courseName: string
  schedule: any
  room?: string
  branch: string
  startDate: string
  endDate: string
  isActive: boolean
  createdAt: string
  updatedAt: string
}

interface MonolithTeacher {
  id: string
  userId: string
  name: string
  subject: string
  branch: string
  photoUrl?: string
  createdAt: string
  updatedAt: string
}

interface MonolithPayment {
  id: string
  studentId: string
  amount: number
  method: string
  status: string
  paidDate?: string
  dueDate?: string
  description?: string
  createdAt: string
  updatedAt: string
}

interface MigrationResult {
  success: boolean
  migrated: number
  errors: string[]
  skipped: number
}

interface MigrationSummary {
  students: MigrationResult
  groups: MigrationResult
  teachers: MigrationResult
  payments: MigrationResult
  overall: {
    totalMigrated: number
    totalErrors: number
    duration: number
  }
}

class MonolithMigrator {
  private staffPrisma: PrismaClient
  private studentsClient: ReturnType<typeof getStudentsClient>
  private logFile: string

  constructor() {
    this.staffPrisma = new PrismaClient()
    this.studentsClient = getStudentsClient()
    this.logFile = path.join(process.cwd(), 'migration-log.txt')
  }

  async log(message: string) {
    const timestamp = new Date().toISOString()
    const logMessage = `[${timestamp}] ${message}\n`
    console.log(message)
    await fs.appendFile(this.logFile, logMessage)
  }

  async loadMonolithData(): Promise<{
    students: MonolithStudent[]
    groups: MonolithGroup[]
    teachers: MonolithTeacher[]
    payments: MonolithPayment[]
  }> {
    // In a real scenario, this would connect to the existing monolithic database
    // For this example, we'll load from JSON files or mock data
    
    try {
      // Try to load from backup files
      const dataDir = path.join(process.cwd(), 'migration-data')
      
      const students = await this.loadJSONFile<MonolithStudent[]>(
        path.join(dataDir, 'students.json'),
        []
      )
      
      const groups = await this.loadJSONFile<MonolithGroup[]>(
        path.join(dataDir, 'groups.json'),
        []
      )
      
      const teachers = await this.loadJSONFile<MonolithTeacher[]>(
        path.join(dataDir, 'teachers.json'),
        []
      )
      
      const payments = await this.loadJSONFile<MonolithPayment[]>(
        path.join(dataDir, 'payments.json'),
        []
      )

      await this.log(`Loaded data: ${students.length} students, ${groups.length} groups, ${teachers.length} teachers, ${payments.length} payments`)
      
      return { students, groups, teachers, payments }
    } catch (error) {
      await this.log(`Error loading monolith data: ${error}`)
      throw error
    }
  }

  private async loadJSONFile<T>(filePath: string, defaultValue: T): Promise<T> {
    try {
      const data = await fs.readFile(filePath, 'utf-8')
      return JSON.parse(data)
    } catch (error) {
      await this.log(`Could not load ${filePath}, using default value`)
      return defaultValue
    }
  }

  async migrateTeachers(teachers: MonolithTeacher[]): Promise<MigrationResult> {
    const result: MigrationResult = {
      success: true,
      migrated: 0,
      errors: [],
      skipped: 0
    }

    await this.log(`Starting teacher migration: ${teachers.length} teachers`)

    for (const teacher of teachers) {
      try {
        // Create teacher in staff database
        // TODO: Implement actual Prisma operations
        // await this.staffPrisma.teacher.upsert({
        //   where: { id: teacher.id },
        //   update: {
        //     name: teacher.name,
        //     subject: teacher.subject,
        //     branch: teacher.branch,
        //     photoUrl: teacher.photoUrl,
        //     updatedAt: new Date()
        //   },
        //   create: {
        //     id: teacher.id,
        //     userId: teacher.userId,
        //     name: teacher.name,
        //     subject: teacher.subject,
        //     branch: teacher.branch,
        //     photoUrl: teacher.photoUrl,
        //     createdAt: new Date(teacher.createdAt),
        //     updatedAt: new Date(teacher.updatedAt)
        //   }
        // })

        // Sync teacher reference to students server
        const syncResult = await this.studentsClient.syncTeacherReference({
          id: teacher.id,
          name: teacher.name,
          subject: teacher.subject,
          branch: teacher.branch,
          photoUrl: teacher.photoUrl
        })

        if (syncResult.success) {
          result.migrated++
          await this.log(`Migrated teacher: ${teacher.name} (${teacher.id})`)
        } else {
          result.errors.push(`Failed to sync teacher ${teacher.id}: ${syncResult.error}`)
        }
      } catch (error) {
        result.errors.push(`Error migrating teacher ${teacher.id}: ${error}`)
      }
    }

    if (result.errors.length > 0) {
      result.success = false
    }

    await this.log(`Teacher migration completed: ${result.migrated} migrated, ${result.errors.length} errors`)
    return result
  }

  async migrateGroups(groups: MonolithGroup[]): Promise<MigrationResult> {
    const result: MigrationResult = {
      success: true,
      migrated: 0,
      errors: [],
      skipped: 0
    }

    await this.log(`Starting group migration: ${groups.length} groups`)

    for (const group of groups) {
      try {
        // Create group in staff database
        // TODO: Implement actual Prisma operations
        // await this.staffPrisma.group.upsert({
        //   where: { id: group.id },
        //   update: {
        //     name: group.name,
        //     teacherId: group.teacherId,
        //     schedule: group.schedule,
        //     room: group.room,
        //     branch: group.branch,
        //     startDate: new Date(group.startDate),
        //     endDate: new Date(group.endDate),
        //     isActive: group.isActive,
        //     updatedAt: new Date()
        //   },
        //   create: {
        //     id: group.id,
        //     name: group.name,
        //     teacherId: group.teacherId,
        //     courseId: group.courseId,
        //     schedule: group.schedule,
        //     room: group.room,
        //     branch: group.branch,
        //     startDate: new Date(group.startDate),
        //     endDate: new Date(group.endDate),
        //     isActive: group.isActive,
        //     createdAt: new Date(group.createdAt),
        //     updatedAt: new Date(group.updatedAt)
        //   }
        // })

        // Sync group reference to students server
        const syncResult = await this.studentsClient.syncGroupReference({
          id: group.id,
          name: group.name,
          teacherReferenceId: group.teacherId,
          courseName: group.courseName,
          schedule: group.schedule,
          room: group.room,
          branch: group.branch,
          startDate: group.startDate,
          endDate: group.endDate,
          isActive: group.isActive
        })

        if (syncResult.success) {
          result.migrated++
          await this.log(`Migrated group: ${group.name} (${group.id})`)
        } else {
          result.errors.push(`Failed to sync group ${group.id}: ${syncResult.error}`)
        }
      } catch (error) {
        result.errors.push(`Error migrating group ${group.id}: ${error}`)
      }
    }

    if (result.errors.length > 0) {
      result.success = false
    }

    await this.log(`Group migration completed: ${result.migrated} migrated, ${result.errors.length} errors`)
    return result
  }

  async migrateStudents(students: MonolithStudent[]): Promise<MigrationResult> {
    const result: MigrationResult = {
      success: true,
      migrated: 0,
      errors: [],
      skipped: 0
    }

    await this.log(`Starting student migration: ${students.length} students`)

    for (const student of students) {
      try {
        // Create student reference in staff database
        // TODO: Implement actual Prisma operations
        // await this.staffPrisma.studentReference.upsert({
        //   where: { id: student.id },
        //   update: {
        //     name: student.name,
        //     phone: student.phone,
        //     status: student.status,
        //     branch: student.branch,
        //     level: student.level,
        //     emergencyContact: student.emergencyContact,
        //     currentGroupId: student.currentGroupId,
        //     updatedAt: new Date()
        //   },
        //   create: {
        //     id: student.id,
        //     name: student.name,
        //     phone: student.phone,
        //     email: student.email,
        //     level: student.level,
        //     status: student.status,
        //     branch: student.branch,
        //     emergencyContact: student.emergencyContact,
        //     dateOfBirth: student.dateOfBirth ? new Date(student.dateOfBirth) : null,
        //     address: student.address,
        //     currentGroupId: student.currentGroupId,
        //     createdAt: new Date(student.createdAt),
        //     updatedAt: new Date(student.updatedAt)
        //   }
        // })

        // Create full student record in students server
        const createResult = await this.studentsClient.createStudent({
          name: student.name,
          phone: student.phone,
          email: student.email,
          level: student.level,
          branch: student.branch,
          emergencyContact: student.emergencyContact,
          dateOfBirth: student.dateOfBirth,
          address: student.address
        })

        if (createResult.success) {
          result.migrated++
          await this.log(`Migrated student: ${student.name} (${student.id})`)
        } else {
          result.errors.push(`Failed to create student ${student.id}: ${createResult.error}`)
        }
      } catch (error) {
        result.errors.push(`Error migrating student ${student.id}: ${error}`)
      }
    }

    if (result.errors.length > 0) {
      result.success = false
    }

    await this.log(`Student migration completed: ${result.migrated} migrated, ${result.errors.length} errors`)
    return result
  }

  async migratePayments(payments: MonolithPayment[]): Promise<MigrationResult> {
    const result: MigrationResult = {
      success: true,
      migrated: 0,
      errors: [],
      skipped: 0
    }

    await this.log(`Starting payment migration: ${payments.length} payments`)

    for (const payment of payments) {
      try {
        // Create payment overview in staff database
        // TODO: Implement actual Prisma operations
        // await this.staffPrisma.paymentOverview.upsert({
        //   where: { id: payment.id },
        //   update: {
        //     amount: payment.amount,
        //     method: payment.method,
        //     status: payment.status,
        //     paidDate: payment.paidDate ? new Date(payment.paidDate) : null,
        //     dueDate: payment.dueDate ? new Date(payment.dueDate) : null,
        //     description: payment.description,
        //     updatedAt: new Date()
        //   },
        //   create: {
        //     id: payment.id,
        //     studentReferenceId: payment.studentId,
        //     amount: payment.amount,
        //     method: payment.method,
        //     status: payment.status,
        //     paidDate: payment.paidDate ? new Date(payment.paidDate) : null,
        //     dueDate: payment.dueDate ? new Date(payment.dueDate) : null,
        //     description: payment.description,
        //     createdAt: new Date(payment.createdAt),
        //     updatedAt: new Date(payment.updatedAt)
        //   }
        // })

        // Record payment in students server
        const recordResult = await this.studentsClient.recordPayment({
          studentId: payment.studentId,
          amount: payment.amount,
          method: payment.method as 'CASH' | 'CARD',
          description: payment.description,
          dueDate: payment.dueDate
        })

        if (recordResult.success) {
          result.migrated++
          await this.log(`Migrated payment: ${payment.id} for student ${payment.studentId}`)
        } else {
          result.errors.push(`Failed to record payment ${payment.id}: ${recordResult.error}`)
        }
      } catch (error) {
        result.errors.push(`Error migrating payment ${payment.id}: ${error}`)
      }
    }

    if (result.errors.length > 0) {
      result.success = false
    }

    await this.log(`Payment migration completed: ${result.migrated} migrated, ${result.errors.length} errors`)
    return result
  }

  async runMigration(): Promise<MigrationSummary> {
    const startTime = Date.now()
    
    await this.log('Starting monolith to multi-server migration')
    
    try {
      // Load data from monolithic system
      const data = await this.loadMonolithData()
      
      // Run migrations in order (teachers first, then groups, then students, then payments)
      const teachers = await this.migrateTeachers(data.teachers)
      const groups = await this.migrateGroups(data.groups)
      const students = await this.migrateStudents(data.students)
      const payments = await this.migratePayments(data.payments)
      
      const duration = Date.now() - startTime
      const totalMigrated = teachers.migrated + groups.migrated + students.migrated + payments.migrated
      const totalErrors = teachers.errors.length + groups.errors.length + students.errors.length + payments.errors.length
      
      const summary: MigrationSummary = {
        students,
        groups,
        teachers,
        payments,
        overall: {
          totalMigrated,
          totalErrors,
          duration
        }
      }
      
      await this.log(`Migration completed in ${duration}ms`)
      await this.log(`Total migrated: ${totalMigrated}, Total errors: ${totalErrors}`)
      
      return summary
    } catch (error) {
      await this.log(`Migration failed: ${error}`)
      throw error
    } finally {
      await this.staffPrisma.$disconnect()
    }
  }
}

// Main execution
async function main() {
  const migrator = new MonolithMigrator()
  
  try {
    const summary = await migrator.runMigration()
    
    console.log('\n=== Migration Summary ===')
    console.log(`Teachers: ${summary.teachers.migrated} migrated, ${summary.teachers.errors.length} errors`)
    console.log(`Groups: ${summary.groups.migrated} migrated, ${summary.groups.errors.length} errors`)
    console.log(`Students: ${summary.students.migrated} migrated, ${summary.students.errors.length} errors`)
    console.log(`Payments: ${summary.payments.migrated} migrated, ${summary.payments.errors.length} errors`)
    console.log(`\nTotal: ${summary.overall.totalMigrated} migrated, ${summary.overall.totalErrors} errors`)
    console.log(`Duration: ${summary.overall.duration}ms`)
    
    if (summary.overall.totalErrors > 0) {
      console.log('\nErrors occurred during migration. Check migration-log.txt for details.')
      process.exit(1)
    } else {
      console.log('\nMigration completed successfully!')
      process.exit(0)
    }
  } catch (error) {
    console.error('Migration failed:', error)
    process.exit(1)
  }
}

if (require.main === module) {
  main()
}
