import { withAuth } from "next-auth/middleware"
import { NextResponse } from "next/server"
import { createVPNAccessControl } from "./lib/middleware/vpn-access"

// Initialize VPN access control
const vpnControl = createVPNAccessControl()

export default withAuth(
  function middleware(req) {
    // Apply VPN access control first
    const vpnResponse = vpnControl.createMiddleware()(req)
    if (vpnResponse) {
      return vpnResponse
    }

    // Add security headers
    const response = NextResponse.next()

    // Security headers
    response.headers.set('X-Frame-Options', 'DENY')
    response.headers.set('X-Content-Type-Options', 'nosniff')
    response.headers.set('X-XSS-Protection', '1; mode=block')
    response.headers.set('Referrer-Policy', 'strict-origin-when-cross-origin')
    response.headers.set('Permissions-Policy', 'camera=(), microphone=(), geolocation=()')

    // HSTS header for HTTPS
    if (req.nextUrl.protocol === 'https:') {
      response.headers.set('Strict-Transport-Security', 'max-age=31536000; includeSubDomains; preload')
    }

    // CSP header for additional security
    const cspHeader = [
      "default-src 'self'",
      "script-src 'self' 'unsafe-eval' 'unsafe-inline'",
      "style-src 'self' 'unsafe-inline'",
      "img-src 'self' data: blob: https:",
      "font-src 'self' data:",
      "connect-src 'self' https:",
      "frame-ancestors 'none'",
      "base-uri 'self'",
      "form-action 'self'"
    ].join('; ')

    response.headers.set('Content-Security-Policy', cspHeader)

    // Add debug headers in development
    if (process.env.NODE_ENV === 'development') {
      const clientIP = req.headers.get('x-forwarded-for') ||
                      req.headers.get('x-real-ip') ||
                      req.ip || 'unknown'
      response.headers.set('X-Debug-Client-IP', clientIP)
      response.headers.set('X-Debug-VPN-Required', process.env.VPN_REQUIRED || 'false')
    }

    return response
  },
  {
    callbacks: {
      authorized: ({ token, req }) => {
        // Allow access to auth pages without token
        if (req.nextUrl.pathname.startsWith("/auth")) {
          return true
        }

        // Allow access to health check and public assets
        if (req.nextUrl.pathname.startsWith("/api/health") ||
            req.nextUrl.pathname.startsWith("/_next") ||
            req.nextUrl.pathname === "/favicon.ico" ||
            req.nextUrl.pathname === "/manifest.json" ||
            req.nextUrl.pathname === "/sw.js" ||
            req.nextUrl.pathname.startsWith("/icons")) {
          return true
        }

        // Require token for all other pages
        return !!token
      },
    },
  }
)

export const config = {
  matcher: [
    /*
     * Match all request paths except for the ones starting with:
     * - api/auth (NextAuth.js routes)
     * - _next/static (static files)
     * - _next/image (image optimization files)
     * - favicon.ico (favicon file)
     * - manifest.json (PWA manifest)
     * - sw.js (service worker)
     * - icons (PWA icons)
     */
    "/((?!api/auth|_next/static|_next/image|favicon.ico|manifest.json|sw.js|icons).*)",
  ],
}
