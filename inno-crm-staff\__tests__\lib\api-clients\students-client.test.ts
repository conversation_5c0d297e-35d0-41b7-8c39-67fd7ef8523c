import { StudentsApiClient } from '@/lib/api-clients/students-client'
import crypto from 'crypto'

// Mock fetch globally
global.fetch = jest.fn()

describe('StudentsApiClient', () => {
  let client: StudentsApiClient
  const mockConfig = {
    baseUrl: 'https://students.example.com',
    apiKey: 'test-api-key',
    secretKey: 'test-secret-key'
  }

  beforeEach(() => {
    client = new StudentsApiClient(mockConfig)
    jest.clearAllMocks()
  })

  describe('constructor', () => {
    it('should initialize with provided config', () => {
      expect(client).toBeInstanceOf(StudentsApiClient)
    })
  })

  describe('generateSignature', () => {
    it('should generate consistent HMAC signatures', () => {
      const method = 'POST'
      const path = '/api/test'
      const body = '{"test": "data"}'
      const timestamp = '2024-01-01T00:00:00.000Z'

      // Access private method through bracket notation
      const signature1 = (client as any).generateSignature(method, path, body, timestamp)
      const signature2 = (client as any).generateSignature(method, path, body, timestamp)

      expect(signature1).toBe(signature2)
      expect(signature1).toHaveLength(64) // SHA256 hex string length
    })

    it('should generate different signatures for different inputs', () => {
      const timestamp = '2024-01-01T00:00:00.000Z'
      
      const sig1 = (client as any).generateSignature('POST', '/api/test', '{}', timestamp)
      const sig2 = (client as any).generateSignature('GET', '/api/test', '{}', timestamp)
      const sig3 = (client as any).generateSignature('POST', '/api/other', '{}', timestamp)

      expect(sig1).not.toBe(sig2)
      expect(sig1).not.toBe(sig3)
      expect(sig2).not.toBe(sig3)
    })
  })

  describe('makeRequest', () => {
    it('should make successful API requests', async () => {
      const mockResponse = { success: true, data: { id: '1', name: 'Test' } }
      
      ;(fetch as jest.Mock).mockResolvedValueOnce({
        ok: true,
        json: async () => mockResponse
      })

      const result = await (client as any).makeRequest('GET', '/api/test')

      expect(fetch).toHaveBeenCalledWith(
        'https://students.example.com/api/test',
        expect.objectContaining({
          method: 'GET',
          headers: expect.objectContaining({
            'Content-Type': 'application/json',
            'X-API-Key': 'test-api-key',
            'X-Server-Source': 'STAFF',
            'X-Signature': expect.any(String),
            'X-Timestamp': expect.any(String)
          })
        })
      )

      expect(result.success).toBe(true)
      expect(result.data).toEqual(mockResponse)
    })

    it('should handle API errors', async () => {
      ;(fetch as jest.Mock).mockResolvedValueOnce({
        ok: false,
        status: 400,
        statusText: 'Bad Request',
        json: async () => ({ error: 'Invalid data' })
      })

      const result = await (client as any).makeRequest('POST', '/api/test', { invalid: 'data' })

      expect(result.success).toBe(false)
      expect(result.error).toBe('Invalid data')
    })

    it('should handle network errors', async () => {
      ;(fetch as jest.Mock).mockRejectedValueOnce(new Error('Network error'))

      const result = await (client as any).makeRequest('GET', '/api/test')

      expect(result.success).toBe(false)
      expect(result.error).toBe('Network error')
    })

    it('should include request body for POST requests', async () => {
      const testData = { name: 'Test Student', phone: '+998901234567' }
      
      ;(fetch as jest.Mock).mockResolvedValueOnce({
        ok: true,
        json: async () => ({ success: true })
      })

      await (client as any).makeRequest('POST', '/api/students', testData)

      expect(fetch).toHaveBeenCalledWith(
        'https://students.example.com/api/students',
        expect.objectContaining({
          method: 'POST',
          body: JSON.stringify(testData)
        })
      )
    })
  })

  describe('createStudent', () => {
    it('should create a student successfully', async () => {
      const studentData = {
        name: 'John Doe',
        phone: '+998901234567',
        email: '<EMAIL>',
        level: 'B1',
        branch: 'main'
      }

      const mockResponse = {
        id: 'student-1',
        ...studentData,
        status: 'ACTIVE',
        createdAt: '2024-01-01T00:00:00.000Z',
        updatedAt: '2024-01-01T00:00:00.000Z'
      }

      ;(fetch as jest.Mock).mockResolvedValueOnce({
        ok: true,
        json: async () => mockResponse
      })

      const result = await client.createStudent(studentData)

      expect(result.success).toBe(true)
      expect(result.data).toEqual(mockResponse)
      expect(fetch).toHaveBeenCalledWith(
        'https://students.example.com/api/inter-server/students',
        expect.objectContaining({
          method: 'POST',
          body: JSON.stringify(studentData)
        })
      )
    })
  })

  describe('updateStudent', () => {
    it('should update a student successfully', async () => {
      const studentId = 'student-1'
      const updateData = { name: 'John Smith', level: 'B2' }

      const mockResponse = {
        id: studentId,
        name: 'John Smith',
        level: 'B2',
        updatedAt: '2024-01-02T00:00:00.000Z'
      }

      ;(fetch as jest.Mock).mockResolvedValueOnce({
        ok: true,
        json: async () => mockResponse
      })

      const result = await client.updateStudent(studentId, updateData)

      expect(result.success).toBe(true)
      expect(result.data).toEqual(mockResponse)
      expect(fetch).toHaveBeenCalledWith(
        `https://students.example.com/api/inter-server/students/${studentId}`,
        expect.objectContaining({
          method: 'PUT',
          body: JSON.stringify(updateData)
        })
      )
    })
  })

  describe('getStudent', () => {
    it('should get a student successfully', async () => {
      const studentId = 'student-1'
      const mockResponse = {
        id: studentId,
        name: 'John Doe',
        phone: '+998901234567',
        level: 'B1',
        status: 'ACTIVE'
      }

      ;(fetch as jest.Mock).mockResolvedValueOnce({
        ok: true,
        json: async () => mockResponse
      })

      const result = await client.getStudent(studentId)

      expect(result.success).toBe(true)
      expect(result.data).toEqual(mockResponse)
      expect(fetch).toHaveBeenCalledWith(
        `https://students.example.com/api/inter-server/students/${studentId}`,
        expect.objectContaining({
          method: 'GET'
        })
      )
    })
  })

  describe('deleteStudent', () => {
    it('should delete a student successfully', async () => {
      const studentId = 'student-1'

      ;(fetch as jest.Mock).mockResolvedValueOnce({
        ok: true,
        json: async () => ({ message: 'Student deleted successfully' })
      })

      const result = await client.deleteStudent(studentId)

      expect(result.success).toBe(true)
      expect(fetch).toHaveBeenCalledWith(
        `https://students.example.com/api/inter-server/students/${studentId}`,
        expect.objectContaining({
          method: 'DELETE'
        })
      )
    })
  })

  describe('recordPayment', () => {
    it('should record a payment successfully', async () => {
      const paymentData = {
        studentId: 'student-1',
        amount: 500000,
        method: 'CASH' as const,
        description: 'Monthly tuition'
      }

      const mockResponse = {
        id: 'payment-1',
        ...paymentData,
        status: 'PAID',
        createdAt: '2024-01-01T00:00:00.000Z'
      }

      ;(fetch as jest.Mock).mockResolvedValueOnce({
        ok: true,
        json: async () => mockResponse
      })

      const result = await client.recordPayment(paymentData)

      expect(result.success).toBe(true)
      expect(result.data).toEqual(mockResponse)
      expect(fetch).toHaveBeenCalledWith(
        'https://students.example.com/api/inter-server/payments',
        expect.objectContaining({
          method: 'POST',
          body: JSON.stringify(paymentData)
        })
      )
    })
  })

  describe('healthCheck', () => {
    it('should perform health check successfully', async () => {
      const mockResponse = {
        status: 'healthy',
        timestamp: '2024-01-01T00:00:00.000Z'
      }

      ;(fetch as jest.Mock).mockResolvedValueOnce({
        ok: true,
        json: async () => mockResponse
      })

      const result = await client.healthCheck()

      expect(result.success).toBe(true)
      expect(result.data).toEqual(mockResponse)
      expect(fetch).toHaveBeenCalledWith(
        'https://students.example.com/api/health',
        expect.objectContaining({
          method: 'GET'
        })
      )
    })
  })
})
