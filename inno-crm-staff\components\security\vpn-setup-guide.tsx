"use client"

import { useState } from 'react'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { But<PERSON> } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>onte<PERSON>, <PERSON><PERSON>List, TabsTrigger } from '@/components/ui/tabs'
import { Alert, AlertDescription, AlertTitle } from '@/components/ui/alert'
import { 
  Shield, 
  Download, 
  Smartphone, 
  Monitor, 
  Apple, 
  Wifi, 
  AlertTriangle,
  CheckCircle,
  Copy,
  ExternalLink
} from 'lucide-react'
import { VPNConfigHelper } from '@/lib/utils/vpn-config'

export function VPNSetupGuide() {
  const [copiedConfig, setCopiedConfig] = useState(false)

  const copyToClipboard = (text: string) => {
    navigator.clipboard.writeText(text)
    setCopiedConfig(true)
    setTimeout(() => setCopiedConfig(false), 2000)
  }

  const openVPNInstructions = VPNConfigHelper.getSetupInstructions('openvpn')
  const wireGuardInstructions = VPNConfigHelper.getSetupInstructions('wireguard')
  const troubleshootingTips = VPNConfigHelper.getTroubleshootingTips()

  return (
    <div className="space-y-6">
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center space-x-2">
            <Shield className="h-5 w-5 text-blue-600" />
            <span>VPN Setup Guide</span>
          </CardTitle>
          <CardDescription>
            Follow these instructions to set up VPN access for the staff portal
          </CardDescription>
        </CardHeader>
        <CardContent>
          <Alert className="mb-6">
            <AlertTriangle className="h-4 w-4" />
            <AlertTitle>Important</AlertTitle>
            <AlertDescription>
              VPN access is required to use the staff portal. Please contact your IT administrator 
              to obtain VPN configuration files and credentials.
            </AlertDescription>
          </Alert>

          <Tabs defaultValue="overview" className="w-full">
            <TabsList className="grid w-full grid-cols-4">
              <TabsTrigger value="overview">Overview</TabsTrigger>
              <TabsTrigger value="windows">Windows</TabsTrigger>
              <TabsTrigger value="mobile">Mobile</TabsTrigger>
              <TabsTrigger value="troubleshooting">Help</TabsTrigger>
            </TabsList>

            <TabsContent value="overview" className="space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <Card>
                  <CardHeader>
                    <CardTitle className="text-lg">OpenVPN</CardTitle>
                    <CardDescription>Recommended for most users</CardDescription>
                  </CardHeader>
                  <CardContent>
                    <div className="space-y-2">
                      <Badge variant="default">Most Compatible</Badge>
                      <p className="text-sm text-gray-600">
                        Works on all platforms with excellent security and stability.
                      </p>
                      <Button variant="outline" size="sm" asChild>
                        <a href="https://openvpn.net/client/" target="_blank" rel="noopener noreferrer">
                          <Download className="h-4 w-4 mr-2" />
                          Download OpenVPN
                          <ExternalLink className="h-4 w-4 ml-2" />
                        </a>
                      </Button>
                    </div>
                  </CardContent>
                </Card>

                <Card>
                  <CardHeader>
                    <CardTitle className="text-lg">WireGuard</CardTitle>
                    <CardDescription>Modern and fast</CardDescription>
                  </CardHeader>
                  <CardContent>
                    <div className="space-y-2">
                      <Badge variant="secondary">High Performance</Badge>
                      <p className="text-sm text-gray-600">
                        Newer protocol with better performance and battery life.
                      </p>
                      <Button variant="outline" size="sm" asChild>
                        <a href="https://www.wireguard.com/install/" target="_blank" rel="noopener noreferrer">
                          <Download className="h-4 w-4 mr-2" />
                          Download WireGuard
                          <ExternalLink className="h-4 w-4 ml-2" />
                        </a>
                      </Button>
                    </div>
                  </CardContent>
                </Card>
              </div>

              <Card>
                <CardHeader>
                  <CardTitle className="text-lg">Network Information</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm">
                    <div>
                      <strong>VPN Server:</strong> vpn.innovative-centre.uz
                    </div>
                    <div>
                      <strong>Port:</strong> 1194 (UDP)
                    </div>
                    <div>
                      <strong>Protocol:</strong> OpenVPN / WireGuard
                    </div>
                    <div>
                      <strong>Encryption:</strong> AES-256-GCM
                    </div>
                  </div>
                </CardContent>
              </Card>
            </TabsContent>

            <TabsContent value="windows" className="space-y-4">
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center space-x-2">
                    <Monitor className="h-5 w-5" />
                    <span>Windows Setup</span>
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="space-y-4">
                    <div>
                      <h4 className="font-semibold mb-2">OpenVPN Setup:</h4>
                      <ol className="list-decimal list-inside space-y-2 text-sm">
                        {openVPNInstructions.map((instruction, index) => (
                          <li key={index}>{instruction}</li>
                        ))}
                      </ol>
                    </div>

                    <div>
                      <h4 className="font-semibold mb-2">WireGuard Setup:</h4>
                      <ol className="list-decimal list-inside space-y-2 text-sm">
                        {wireGuardInstructions.map((instruction, index) => (
                          <li key={index}>{instruction}</li>
                        ))}
                      </ol>
                    </div>
                  </div>
                </CardContent>
              </Card>
            </TabsContent>

            <TabsContent value="mobile" className="space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <Card>
                  <CardHeader>
                    <CardTitle className="flex items-center space-x-2">
                      <Smartphone className="h-5 w-5" />
                      <span>Android</span>
                    </CardTitle>
                  </CardHeader>
                  <CardContent className="space-y-3">
                    <div>
                      <h5 className="font-medium">OpenVPN for Android</h5>
                      <p className="text-sm text-gray-600 mb-2">
                        Download from Google Play Store
                      </p>
                      <Button variant="outline" size="sm" asChild>
                        <a href="https://play.google.com/store/apps/details?id=net.openvpn.openvpn" target="_blank">
                          <Download className="h-4 w-4 mr-2" />
                          Get OpenVPN
                        </a>
                      </Button>
                    </div>
                    <div>
                      <h5 className="font-medium">WireGuard for Android</h5>
                      <p className="text-sm text-gray-600 mb-2">
                        Download from Google Play Store
                      </p>
                      <Button variant="outline" size="sm" asChild>
                        <a href="https://play.google.com/store/apps/details?id=com.wireguard.android" target="_blank">
                          <Download className="h-4 w-4 mr-2" />
                          Get WireGuard
                        </a>
                      </Button>
                    </div>
                  </CardContent>
                </Card>

                <Card>
                  <CardHeader>
                    <CardTitle className="flex items-center space-x-2">
                      <Apple className="h-5 w-5" />
                      <span>iOS</span>
                    </CardTitle>
                  </CardHeader>
                  <CardContent className="space-y-3">
                    <div>
                      <h5 className="font-medium">OpenVPN Connect</h5>
                      <p className="text-sm text-gray-600 mb-2">
                        Download from App Store
                      </p>
                      <Button variant="outline" size="sm" asChild>
                        <a href="https://apps.apple.com/app/openvpn-connect/id590379981" target="_blank">
                          <Download className="h-4 w-4 mr-2" />
                          Get OpenVPN
                        </a>
                      </Button>
                    </div>
                    <div>
                      <h5 className="font-medium">WireGuard</h5>
                      <p className="text-sm text-gray-600 mb-2">
                        Download from App Store
                      </p>
                      <Button variant="outline" size="sm" asChild>
                        <a href="https://apps.apple.com/app/wireguard/id1441195209" target="_blank">
                          <Download className="h-4 w-4 mr-2" />
                          Get WireGuard
                        </a>
                      </Button>
                    </div>
                  </CardContent>
                </Card>
              </div>

              <Alert>
                <Smartphone className="h-4 w-4" />
                <AlertTitle>Mobile Setup Tips</AlertTitle>
                <AlertDescription>
                  <ul className="list-disc list-inside mt-2 space-y-1 text-sm">
                    <li>Import configuration files via email or file sharing</li>
                    <li>Scan QR codes for quick WireGuard setup</li>
                    <li>Enable "Connect on Demand" for automatic connection</li>
                    <li>Allow VPN apps to run in background</li>
                  </ul>
                </AlertDescription>
              </Alert>
            </TabsContent>

            <TabsContent value="troubleshooting" className="space-y-4">
              <Card>
                <CardHeader>
                  <CardTitle>Troubleshooting</CardTitle>
                  <CardDescription>
                    Common issues and solutions
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  <div className="space-y-4">
                    <h4 className="font-semibold">Common Solutions:</h4>
                    <ul className="space-y-2">
                      {troubleshootingTips.map((tip, index) => (
                        <li key={index} className="flex items-start space-x-2">
                          <CheckCircle className="h-4 w-4 text-green-500 mt-0.5 flex-shrink-0" />
                          <span className="text-sm">{tip}</span>
                        </li>
                      ))}
                    </ul>
                  </div>
                </CardContent>
              </Card>

              <Card>
                <CardHeader>
                  <CardTitle>Contact Support</CardTitle>
                </CardHeader>
                <CardContent>
                  <p className="text-sm text-gray-600 mb-4">
                    If you continue to experience issues, please contact IT support:
                  </p>
                  <div className="space-y-2 text-sm">
                    <div><strong>Email:</strong> <EMAIL></div>
                    <div><strong>Phone:</strong> +998 (71) 123-45-67</div>
                    <div><strong>Hours:</strong> Monday - Friday, 9:00 AM - 6:00 PM</div>
                  </div>
                </CardContent>
              </Card>
            </TabsContent>
          </Tabs>
        </CardContent>
      </Card>
    </div>
  )
}
