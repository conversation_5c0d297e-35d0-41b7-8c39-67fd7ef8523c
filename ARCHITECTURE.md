# Innovative Centre CRM - System Architecture

## Overview

The Innovative Centre CRM is a modern, scalable multi-server architecture designed to manage educational operations efficiently. The system is split into specialized portals that communicate securely while maintaining data consistency.

## Architecture Diagram

```mermaid
graph TB
    subgraph "External Access"
        VPN[VPN Server<br/>********/24]
        Internet[Internet]
    end

    subgraph "Staff Portal (VPN Only)"
        Staff[Staff Portal<br/>staff.innovative-centre.uz<br/>Port 3001]
        StaffDB[(Staff Database<br/>PostgreSQL)]
    end

    subgraph "Students Portal (Public)"
        Students[Students Portal<br/>students.innovative-centre.uz<br/>Port 3002]
        StudentsDB[(Students Database<br/>PostgreSQL)]
        SW[Service Worker<br/>PWA Features]
    end

    subgraph "Future Portals"
        Tests[Tests Portal<br/>tests.innovative-centre.uz]
        IELTS[IELTS Portal<br/>ielts.innovative-centre.uz]
    end

    subgraph "Infrastructure"
        Vercel[Vercel<br/>Hosting & CDN]
        Neon[Neon<br/>PostgreSQL Hosting]
    end

    VPN --> Staff
    Internet --> Students
    Internet --> Tests
    Internet --> IELTS

    Staff <--> StaffDB
    Students <--> StudentsDB
    Students --> SW

    Staff <-.->|Secure API| Students
    Staff <-.->|Future| Tests
    Staff <-.->|Future| IELTS

    Staff --> Vercel
    Students --> Vercel
    StaffDB --> Neon
    StudentsDB --> Neon
```

## System Components

### 1. Staff Portal (VPN-Protected)

**Purpose**: Administrative interface for staff management and operations

**Key Features**:
- Student management (CRUD operations)
- Lead tracking and conversion
- Group and class management
- Teacher profiles and assignments
- Payment tracking and financial overview
- Analytics and reporting
- Activity logging and audit trails

**Security**:
- VPN-only access (IP-based restrictions)
- Role-based access control (Admin, Manager, Teacher, Reception)
- Session management with NextAuth.js
- Comprehensive audit logging

**Technology Stack**:
- Next.js 15 with App Router
- TypeScript
- Prisma ORM
- PostgreSQL (Neon)
- NextAuth.js
- Radix UI + Tailwind CSS

### 2. Students Portal (Public Access)

**Purpose**: Student-facing interface with Progressive Web App capabilities

**Key Features**:
- Student dashboard and profile
- Course enrollment and progress tracking
- Assessment and test taking
- Attendance monitoring
- Payment history
- Mobile-first PWA experience

**Progressive Web App**:
- Installable on mobile devices
- Offline functionality with service workers
- Push notifications (ready for implementation)
- Background sync capabilities
- Mobile-optimized interface

**Technology Stack**:
- Next.js 15 with App Router
- TypeScript
- Prisma ORM
- PostgreSQL (Neon)
- PWA features (Service Worker, Manifest)
- Radix UI + Tailwind CSS

### 3. Inter-Server Communication

**Purpose**: Secure data synchronization between portals

**Features**:
- HMAC-based authentication
- Rate limiting and request validation
- Real-time data synchronization
- Reference table management
- Event-driven notifications

**Security Measures**:
- API key authentication
- HMAC signature validation
- Timestamp-based replay attack prevention
- IP-based access control
- Comprehensive request logging

## Data Architecture

### Database Design

Each portal maintains its own database with reference tables for cross-server data:

#### Staff Database Schema
- **Core Tables**: Users, Teachers, Groups, Courses, Leads, Cabinets
- **Reference Tables**: StudentReference (minimal student data)
- **Financial**: PaymentOverview (admin view of all payments)
- **Audit**: ActivityLog, CallRecord
- **Communication**: Message, Announcement

#### Students Database Schema
- **Core Tables**: Users, Students, Enrollments, Payments, Attendance, Assessments
- **Reference Tables**: GroupReference, TeacherReference (synced from staff)
- **Communication**: Message (student-facing)

### Data Synchronization

1. **Reference Data Sync**:
   - Teachers and Groups synced from Staff to Students
   - Student references synced from Students to Staff
   - Automatic sync on data changes

2. **Event-Driven Updates**:
   - Payment notifications
   - Attendance updates
   - Student status changes
   - Group assignments

3. **Consistency Mechanisms**:
   - Sync timestamps and versioning
   - Integrity validation
   - Conflict resolution strategies

## Security Architecture

### VPN Access Control

**Staff Portal Security**:
- Mandatory VPN connection for access
- IP range validation (********/24, ***********/24)
- Bypass paths for health checks and auth
- Development environment bypass option

**Implementation**:
- Next.js middleware for IP validation
- CIDR notation support
- Real-time access monitoring
- Security event logging

### Authentication & Authorization

**Staff Portal**:
- NextAuth.js with session management
- Role-based permissions (Admin, Manager, Teacher, Reception)
- Secure password hashing with bcrypt
- Session timeout and management

**Students Portal**:
- Student-only authentication
- Simplified role structure
- Mobile-friendly auth flow
- PWA-compatible session handling

### API Security

**Inter-Server Communication**:
- HMAC-SHA256 signature validation
- API key authentication
- Timestamp-based replay protection
- Rate limiting per client
- Comprehensive request logging

## Deployment Architecture

### Hosting Strategy

**Vercel Deployment**:
- Staff Portal: Vercel Pro/Enterprise (for IP restrictions)
- Students Portal: Vercel (any tier)
- Automatic deployments from Git
- Environment-specific configurations

**Database Hosting**:
- Neon PostgreSQL for both databases
- Separate database instances for isolation
- Automated backups and point-in-time recovery
- Connection pooling for performance

### Environment Configuration

**Production**:
- VPN access enforced for staff portal
- HTTPS with security headers
- Database connection pooling
- Monitoring and alerting

**Development**:
- VPN bypass option
- Local database development
- Hot reloading and debugging
- Test data seeding

## Performance Considerations

### Optimization Strategies

1. **Database Performance**:
   - Strategic indexing on frequently queried columns
   - Query optimization and monitoring
   - Connection pooling
   - Read replicas for reporting

2. **Application Performance**:
   - Next.js App Router for optimal loading
   - Code splitting and lazy loading
   - Image optimization
   - Static generation where possible

3. **PWA Performance**:
   - Service worker caching strategies
   - Background sync for offline operations
   - Optimized bundle sizes
   - Progressive enhancement

### Monitoring & Analytics

**Application Monitoring**:
- Health check endpoints
- Performance metrics
- Error tracking and alerting
- User activity analytics

**Database Monitoring**:
- Query performance tracking
- Connection pool monitoring
- Storage usage alerts
- Backup verification

## Scalability Considerations

### Horizontal Scaling

**Application Scaling**:
- Stateless application design
- Load balancing via Vercel
- CDN for static assets
- Microservice-ready architecture

**Database Scaling**:
- Read replicas for reporting
- Connection pooling
- Query optimization
- Data archival strategies

### Future Expansion

**Additional Portals**:
- Tests Portal for assessment management
- IELTS Portal for specialized testing
- Mobile apps using shared APIs
- Third-party integrations

**Feature Expansion**:
- Real-time notifications
- Advanced analytics
- AI-powered insights
- Integration with external systems

## Disaster Recovery

### Backup Strategy

**Database Backups**:
- Automated daily backups
- Point-in-time recovery capability
- Cross-region backup storage
- Regular restore testing

**Application Recovery**:
- Git-based version control
- Infrastructure as code
- Environment configuration backup
- Deployment rollback procedures

### Business Continuity

**High Availability**:
- Multi-region deployment capability
- Database failover mechanisms
- CDN for global availability
- Monitoring and alerting systems

**Data Protection**:
- Encryption at rest and in transit
- Access control and audit trails
- GDPR compliance measures
- Data retention policies

## Development Workflow

### Code Organization

**Monorepo Structure**:
- Separate projects for each portal
- Shared utilities and types
- Consistent coding standards
- Automated testing and linting

**Development Process**:
- Feature branch workflow
- Pull request reviews
- Automated testing
- Continuous integration/deployment

### Quality Assurance

**Testing Strategy**:
- Unit tests for business logic
- Integration tests for APIs
- End-to-end testing for critical flows
- Performance testing

**Code Quality**:
- TypeScript for type safety
- ESLint and Prettier for consistency
- Code coverage requirements
- Security scanning
