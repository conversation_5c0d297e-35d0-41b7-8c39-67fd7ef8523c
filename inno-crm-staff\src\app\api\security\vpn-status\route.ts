import { NextRequest, NextResponse } from 'next/server'
import { createVPNAccessControl } from '@/lib/middleware/vpn-access'

export async function GET(request: NextRequest) {
  try {
    const vpnControl = createVPNAccessControl()
    const accessResult = vpnControl.checkAccess(request)

    // Extract client IP
    const clientIP = request.headers.get('x-forwarded-for')?.split(',')[0]?.trim() ||
                    request.headers.get('x-real-ip') ||
                    request.headers.get('cf-connecting-ip') ||
                    request.ip ||
                    'unknown'

    const response = {
      connected: accessResult.allowed,
      clientIP,
      allowedRange: accessResult.matchedRange,
      vpnRequired: process.env.VPN_REQUIRED === 'true',
      lastChecked: new Date().toISOString(),
      reason: accessResult.reason
    }

    return NextResponse.json(response)
  } catch (error) {
    console.error('Error checking VPN status:', error)
    return NextResponse.json(
      { 
        error: 'Failed to check VPN status',
        details: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    )
  }
}
