// VPN configuration utilities and helpers

export interface VPNNetworkConfig {
  name: string
  cidr: string
  description: string
  priority: number
}

export interface VPNServerConfig {
  name: string
  host: string
  port: number
  protocol: 'openvpn' | 'wireguard' | 'ipsec'
  configFile?: string
}

// Predefined VPN network configurations
export const VPN_NETWORKS: VPNNetworkConfig[] = [
  {
    name: 'Main Office VPN',
    cidr: '********/24',
    description: 'Primary VPN network for office access',
    priority: 1
  },
  {
    name: 'Branch Office Network',
    cidr: '***********/24',
    description: 'Local network for branch offices',
    priority: 2
  },
  {
    name: 'Remote Access VPN',
    cidr: '********/24',
    description: 'VPN for remote staff access',
    priority: 3
  },
  {
    name: 'Development Network',
    cidr: '**********/24',
    description: 'Development and testing network',
    priority: 4
  }
]

// VPN server configurations
export const VPN_SERVERS: VPNServerConfig[] = [
  {
    name: 'Primary OpenVPN Server',
    host: 'vpn.innovative-centre.uz',
    port: 1194,
    protocol: 'openvpn',
    configFile: 'innovative-centre.ovpn'
  },
  {
    name: 'Backup VPN Server',
    host: 'vpn-backup.innovative-centre.uz',
    port: 1194,
    protocol: 'openvpn',
    configFile: 'innovative-centre-backup.ovpn'
  }
]

// Utility functions
export class VPNConfigHelper {
  
  // Get all allowed IP ranges from environment
  static getAllowedRanges(): string[] {
    const ranges = process.env.ALLOWED_IP_RANGES || ''
    return ranges.split(',').filter(Boolean).map(range => range.trim())
  }

  // Validate CIDR notation
  static isValidCIDR(cidr: string): boolean {
    const cidrRegex = /^(?:(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\.){3}(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\/(?:[0-9]|[1-2][0-9]|3[0-2])$/
    return cidrRegex.test(cidr)
  }

  // Get network info from CIDR
  static getNetworkInfo(cidr: string): {
    network: string
    mask: number
    hostCount: number
    firstHost: string
    lastHost: string
    broadcast: string
  } | null {
    if (!this.isValidCIDR(cidr)) {
      return null
    }

    const [network, maskStr] = cidr.split('/')
    const mask = parseInt(maskStr, 10)
    const hostBits = 32 - mask
    const hostCount = Math.pow(2, hostBits) - 2 // Subtract network and broadcast

    // Calculate first and last host IPs
    const networkParts = network.split('.').map(Number)
    const networkInt = (networkParts[0] << 24) + (networkParts[1] << 16) + (networkParts[2] << 8) + networkParts[3]
    
    const firstHostInt = networkInt + 1
    const lastHostInt = networkInt + hostCount
    const broadcastInt = networkInt + Math.pow(2, hostBits) - 1

    const intToIP = (int: number) => [
      (int >>> 24) & 255,
      (int >>> 16) & 255,
      (int >>> 8) & 255,
      int & 255
    ].join('.')

    return {
      network,
      mask,
      hostCount,
      firstHost: intToIP(firstHostInt),
      lastHost: intToIP(lastHostInt),
      broadcast: intToIP(broadcastInt)
    }
  }

  // Generate OpenVPN client configuration
  static generateOpenVPNConfig(server: VPNServerConfig, clientName: string): string {
    return `# OpenVPN Client Configuration for ${clientName}
# Server: ${server.name}

client
dev tun
proto udp
remote ${server.host} ${server.port}
resolv-retry infinite
nobind
persist-key
persist-tun

# Security settings
cipher AES-256-GCM
auth SHA256
key-direction 1
remote-cert-tls server
tls-version-min 1.2

# Compression
compress lz4-v2
push "compress lz4-v2"

# Logging
verb 3
mute 20

# Certificate and key files (to be added)
<ca>
# Certificate Authority certificate goes here
</ca>

<cert>
# Client certificate goes here
</cert>

<key>
# Client private key goes here
</key>

<tls-auth>
# TLS authentication key goes here
</tls-auth>
`
  }

  // Generate WireGuard configuration
  static generateWireGuardConfig(server: VPNServerConfig, clientName: string, clientPrivateKey: string, clientIP: string): string {
    return `# WireGuard Client Configuration for ${clientName}
# Server: ${server.name}

[Interface]
PrivateKey = ${clientPrivateKey}
Address = ${clientIP}/24
DNS = *******, *******

[Peer]
PublicKey = SERVER_PUBLIC_KEY_HERE
Endpoint = ${server.host}:${server.port}
AllowedIPs = 0.0.0.0/0
PersistentKeepalive = 25
`
  }

  // Get VPN setup instructions
  static getSetupInstructions(protocol: 'openvpn' | 'wireguard'): string[] {
    if (protocol === 'openvpn') {
      return [
        '1. Download and install OpenVPN client from https://openvpn.net/client/',
        '2. Download the configuration file provided by your administrator',
        '3. Import the configuration file into the OpenVPN client',
        '4. Connect to the VPN using your credentials',
        '5. Verify connection by checking your IP address',
        '6. Access the staff portal at https://staff.innovative-centre.uz'
      ]
    } else {
      return [
        '1. Download and install WireGuard client from https://www.wireguard.com/install/',
        '2. Import the configuration file or scan the QR code provided',
        '3. Activate the VPN tunnel',
        '4. Verify connection by checking your IP address',
        '5. Access the staff portal at https://staff.innovative-centre.uz'
      ]
    }
  }

  // Get troubleshooting tips
  static getTroubleshootingTips(): string[] {
    return [
      'Ensure you are connected to the internet before connecting to VPN',
      'Try different VPN servers if connection fails',
      'Check if your firewall is blocking VPN traffic (ports 1194 UDP for OpenVPN)',
      'Verify your VPN credentials are correct',
      'Try restarting the VPN client application',
      'Contact IT support if you continue to experience issues',
      'For mobile devices, ensure the VPN app has necessary permissions',
      'Check if your ISP blocks VPN traffic and consider using different ports'
    ]
  }

  // Validate environment configuration
  static validateEnvironmentConfig(): {
    valid: boolean
    issues: string[]
    recommendations: string[]
  } {
    const issues: string[] = []
    const recommendations: string[] = []

    // Check if VPN is required
    const vpnRequired = process.env.VPN_REQUIRED === 'true'
    if (!vpnRequired && process.env.NODE_ENV === 'production') {
      issues.push('VPN requirement is disabled in production environment')
      recommendations.push('Enable VPN_REQUIRED=true for production deployment')
    }

    // Check allowed IP ranges
    const allowedRanges = this.getAllowedRanges()
    if (vpnRequired && allowedRanges.length === 0) {
      issues.push('No allowed IP ranges configured')
      recommendations.push('Configure ALLOWED_IP_RANGES environment variable')
    }

    // Validate CIDR notation
    allowedRanges.forEach(range => {
      if (!this.isValidCIDR(range)) {
        issues.push(`Invalid CIDR notation: ${range}`)
        recommendations.push(`Fix CIDR notation for range: ${range}`)
      }
    })

    return {
      valid: issues.length === 0,
      issues,
      recommendations
    }
  }
}

// Export default configuration
export const DEFAULT_VPN_CONFIG = {
  networks: VPN_NETWORKS,
  servers: VPN_SERVERS,
  helper: VPNConfigHelper
}
