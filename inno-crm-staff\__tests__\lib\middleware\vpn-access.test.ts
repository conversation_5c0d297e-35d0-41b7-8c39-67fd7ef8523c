import { VPNAccessControl } from '@/lib/middleware/vpn-access'
import { NextRequest } from 'next/server'

// Mock NextRequest
const createMockRequest = (options: {
  pathname?: string
  ip?: string
  headers?: Record<string, string>
}) => {
  const url = `https://example.com${options.pathname || '/'}`
  const request = {
    nextUrl: new URL(url),
    headers: new Map(Object.entries(options.headers || {})),
    ip: options.ip
  } as unknown as NextRequest

  // Mock headers.get method
  request.headers.get = jest.fn((name: string) => {
    const headers = options.headers || {}
    return headers[name] || null
  })

  return request
}

describe('VPNAccessControl', () => {
  let vpnControl: VPNAccessControl

  beforeEach(() => {
    vpnControl = new VPNAccessControl({
      enabled: true,
      allowedRanges: ['********/24', '***********/24'],
      bypassPaths: ['/api/health', '/auth/signin'],
      developmentBypass: false
    })
  })

  describe('checkAccess', () => {
    it('should allow access when VPN is disabled', () => {
      const vpnDisabled = new VPNAccessControl({
        enabled: false,
        allowedRanges: [],
        bypassPaths: [],
        developmentBypass: false
      })

      const request = createMockRequest({ ip: '*******' })
      const result = vpnDisabled.checkAccess(request)

      expect(result.allowed).toBe(true)
      expect(result.reason).toBe('VPN check disabled')
    })

    it('should allow access for bypass paths', () => {
      const request = createMockRequest({ 
        pathname: '/api/health',
        ip: '*******' 
      })
      const result = vpnControl.checkAccess(request)

      expect(result.allowed).toBe(true)
      expect(result.reason).toContain('bypasses VPN check')
    })

    it('should allow access for IPs in allowed ranges', () => {
      const request = createMockRequest({ 
        ip: '**********',
        headers: { 'x-forwarded-for': '**********' }
      })
      const result = vpnControl.checkAccess(request)

      expect(result.allowed).toBe(true)
      expect(result.clientIP).toBe('**********')
      expect(result.matchedRange).toBe('********/24')
    })

    it('should deny access for IPs not in allowed ranges', () => {
      const request = createMockRequest({ 
        ip: '*******',
        headers: { 'x-forwarded-for': '*******' }
      })
      const result = vpnControl.checkAccess(request)

      expect(result.allowed).toBe(false)
      expect(result.clientIP).toBe('*******')
      expect(result.reason).toContain('not in allowed VPN ranges')
    })

    it('should handle x-forwarded-for header with multiple IPs', () => {
      const request = createMockRequest({ 
        headers: { 'x-forwarded-for': '**********, ***********, *******' }
      })
      const result = vpnControl.checkAccess(request)

      expect(result.allowed).toBe(true)
      expect(result.clientIP).toBe('**********')
    })

    it('should handle invalid IP addresses', () => {
      const request = createMockRequest({ 
        headers: { 'x-forwarded-for': 'invalid-ip' }
      })
      const result = vpnControl.checkAccess(request)

      expect(result.allowed).toBe(false)
      expect(result.reason).toContain('Invalid IP format')
    })

    it('should allow development bypass when enabled', () => {
      const originalEnv = process.env.NODE_ENV
      process.env.NODE_ENV = 'development'

      const vpnWithBypass = new VPNAccessControl({
        enabled: true,
        allowedRanges: ['********/24'],
        bypassPaths: [],
        developmentBypass: true
      })

      const request = createMockRequest({ ip: '*******' })
      const result = vpnWithBypass.checkAccess(request)

      expect(result.allowed).toBe(true)
      expect(result.reason).toBe('Development bypass enabled')

      process.env.NODE_ENV = originalEnv
    })
  })

  describe('createMiddleware', () => {
    it('should return null for allowed requests', () => {
      const middleware = vpnControl.createMiddleware()
      const request = createMockRequest({ 
        pathname: '/api/health',
        ip: '*******' 
      })

      const response = middleware(request)
      expect(response).toBeNull()
    })

    it('should return 403 response for denied requests', () => {
      const middleware = vpnControl.createMiddleware()
      const request = createMockRequest({ 
        pathname: '/dashboard',
        ip: '*******',
        headers: { 'x-forwarded-for': '*******' }
      })

      const response = middleware(request)
      expect(response).not.toBeNull()
      expect(response?.status).toBe(403)
    })
  })

  describe('IP range validation', () => {
    it('should correctly validate CIDR ranges', () => {
      const testCases = [
        { ip: '********', range: '********/24', expected: true },
        { ip: '**********', range: '********/24', expected: true },
        { ip: '********', range: '********/24', expected: false },
        { ip: '***********00', range: '***********/24', expected: true },
        { ip: '*************', range: '***********/24', expected: false },
        { ip: '********', range: '10.0.0.0/8', expected: true },
        { ip: '********', range: '10.0.0.0/8', expected: false }
      ]

      testCases.forEach(({ ip, range, expected }) => {
        const request = createMockRequest({ 
          headers: { 'x-forwarded-for': ip }
        })
        
        const vpn = new VPNAccessControl({
          enabled: true,
          allowedRanges: [range],
          bypassPaths: [],
          developmentBypass: false
        })

        const result = vpn.checkAccess(request)
        expect(result.allowed).toBe(expected)
      })
    })
  })

  describe('bypass paths', () => {
    it('should handle wildcard bypass paths', () => {
      const vpnWithWildcard = new VPNAccessControl({
        enabled: true,
        allowedRanges: ['********/24'],
        bypassPaths: ['/api/*', '/auth/*'],
        developmentBypass: false
      })

      const testPaths = [
        { path: '/api/health', expected: true },
        { path: '/api/auth/signin', expected: true },
        { path: '/auth/signin', expected: true },
        { path: '/auth/callback', expected: true },
        { path: '/dashboard', expected: false },
        { path: '/api', expected: false } // exact match, not wildcard
      ]

      testPaths.forEach(({ path, expected }) => {
        const request = createMockRequest({ 
          pathname: path,
          ip: '*******' 
        })
        const result = vpnWithWildcard.checkAccess(request)
        expect(result.allowed).toBe(expected)
      })
    })
  })
})
