{"name": "inno-crm-staff", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev --port 3001", "build": "prisma generate && next build", "start": "next start", "lint": "next lint", "db:push": "prisma db push", "db:studio": "prisma studio", "db:generate": "prisma generate", "db:migrate": "prisma migrate dev", "postinstall": "prisma generate", "type-check": "tsc --noEmit", "vercel-build": "prisma generate && next build", "migrate:export": "tsx scripts/migration/export-monolith-data.ts", "migrate:from-monolith": "tsx scripts/migration/migrate-from-monolith.ts", "migrate:validate": "tsx scripts/migration/validate-migration.ts", "migrate:full": "npm run migrate:export && npm run migrate:from-monolith && npm run migrate:validate"}, "dependencies": {"@hookform/resolvers": "^5.1.1", "@next-auth/prisma-adapter": "^1.0.7", "@prisma/client": "^6.10.1", "@radix-ui/react-dialog": "^1.1.14", "@radix-ui/react-dropdown-menu": "^2.1.15", "@radix-ui/react-label": "^2.1.7", "@radix-ui/react-select": "^2.2.5", "@radix-ui/react-tabs": "^1.1.12", "@radix-ui/react-toast": "^1.2.14", "@types/bcryptjs": "^2.4.6", "bcryptjs": "^3.0.2", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "date-fns": "^4.1.0", "lucide-react": "^0.518.0", "next": "15.3.4", "next-auth": "^4.24.11", "prisma": "^6.10.1", "react": "^19.0.0", "react-dom": "^19.0.0", "react-hook-form": "^7.58.1", "recharts": "^2.15.3", "tailwind-merge": "^3.3.1", "zod": "^3.25.67", "zustand": "^5.0.5"}, "devDependencies": {"@eslint/eslintrc": "^3", "@tailwindcss/postcss": "^4", "@types/node": "^20.19.1", "@types/react": "^19", "@types/react-dom": "^19", "eslint": "^9", "eslint-config-next": "15.3.4", "tailwindcss": "^4", "tsx": "^4.20.3", "typescript": "^5"}}