#!/usr/bin/env tsx

/**
 * Validation script to verify data integrity after migration
 * 
 * Usage: npm run validate:migration
 */

import { PrismaClient } from '@prisma/client'
import { getStudentsClient } from '../../lib/api-clients/students-client'
import fs from 'fs/promises'
import path from 'path'

interface ValidationResult {
  table: string
  passed: boolean
  issues: string[]
  recommendations: string[]
  recordCount: {
    expected: number
    actual: number
    missing: number
  }
}

interface ValidationSummary {
  overall: {
    passed: boolean
    totalIssues: number
    validationTime: number
  }
  results: ValidationResult[]
}

class MigrationValidator {
  private staffPrisma: PrismaClient
  private studentsClient: ReturnType<typeof getStudentsClient>
  private exportDir: string

  constructor() {
    this.staffPrisma = new PrismaClient()
    this.studentsClient = getStudentsClient()
    this.exportDir = path.join(process.cwd(), 'migration-data')
  }

  async loadExportedData() {
    const loadFile = async (filename: string) => {
      try {
        const data = await fs.readFile(path.join(this.exportDir, filename), 'utf-8')
        return JSON.parse(data)
      } catch (error) {
        console.warn(`Could not load ${filename}:`, error)
        return []
      }
    }

    return {
      students: await loadFile('students.json'),
      groups: await loadFile('groups.json'),
      teachers: await loadFile('teachers.json'),
      payments: await loadFile('payments.json'),
      attendance: await loadFile('attendance.json'),
      assessments: await loadFile('assessments.json')
    }
  }

  async validateStudents(exportedStudents: any[]): Promise<ValidationResult> {
    const result: ValidationResult = {
      table: 'students',
      passed: true,
      issues: [],
      recommendations: [],
      recordCount: {
        expected: exportedStudents.length,
        actual: 0,
        missing: 0
      }
    }

    try {
      // Check staff database student references
      // TODO: Implement actual Prisma queries
      // const staffStudents = await this.staffPrisma.studentReference.findMany()
      const staffStudents: any[] = [] // Mock for now
      
      // Check students server
      const studentsResponse = await this.studentsClient.getStudent('test-id')
      // Note: In real implementation, we'd have a method to get all students
      
      result.recordCount.actual = staffStudents.length
      result.recordCount.missing = result.recordCount.expected - result.recordCount.actual

      // Validate data integrity
      for (const exportedStudent of exportedStudents) {
        const staffStudent = staffStudents.find(s => s.id === exportedStudent.id)
        
        if (!staffStudent) {
          result.issues.push(`Student ${exportedStudent.id} (${exportedStudent.name}) not found in staff database`)
          result.passed = false
          continue
        }

        // Validate field consistency
        if (staffStudent.name !== exportedStudent.name) {
          result.issues.push(`Student ${exportedStudent.id}: name mismatch (staff: ${staffStudent.name}, exported: ${exportedStudent.name})`)
          result.passed = false
        }

        if (staffStudent.phone !== exportedStudent.phone) {
          result.issues.push(`Student ${exportedStudent.id}: phone mismatch`)
          result.passed = false
        }

        if (staffStudent.status !== exportedStudent.status) {
          result.issues.push(`Student ${exportedStudent.id}: status mismatch`)
          result.passed = false
        }
      }

      // Check for orphaned records
      for (const staffStudent of staffStudents) {
        const exportedStudent = exportedStudents.find(s => s.id === staffStudent.id)
        if (!exportedStudent) {
          result.issues.push(`Orphaned student in staff database: ${staffStudent.id} (${staffStudent.name})`)
          result.passed = false
        }
      }

      if (result.recordCount.missing > 0) {
        result.recommendations.push(`${result.recordCount.missing} students are missing from the staff database`)
      }

      if (result.issues.length === 0) {
        result.recommendations.push('Student migration appears successful')
      }

    } catch (error) {
      result.issues.push(`Validation error: ${error}`)
      result.passed = false
    }

    return result
  }

  async validateGroups(exportedGroups: any[]): Promise<ValidationResult> {
    const result: ValidationResult = {
      table: 'groups',
      passed: true,
      issues: [],
      recommendations: [],
      recordCount: {
        expected: exportedGroups.length,
        actual: 0,
        missing: 0
      }
    }

    try {
      // Check staff database groups
      // TODO: Implement actual Prisma queries
      // const staffGroups = await this.staffPrisma.group.findMany()
      const staffGroups: any[] = [] // Mock for now
      
      // Check students server group references
      const groupRefsResponse = await this.studentsClient.getGroupReferences()
      const studentGroupRefs = groupRefsResponse.data || []

      result.recordCount.actual = staffGroups.length

      // Validate groups exist in both systems
      for (const exportedGroup of exportedGroups) {
        const staffGroup = staffGroups.find(g => g.id === exportedGroup.id)
        const studentGroupRef = studentGroupRefs.find(g => g.id === exportedGroup.id)

        if (!staffGroup) {
          result.issues.push(`Group ${exportedGroup.id} (${exportedGroup.name}) not found in staff database`)
          result.passed = false
        }

        if (!studentGroupRef) {
          result.issues.push(`Group ${exportedGroup.id} (${exportedGroup.name}) not found in students database`)
          result.passed = false
        }

        // Validate consistency between staff and students systems
        if (staffGroup && studentGroupRef) {
          if (staffGroup.name !== studentGroupRef.name) {
            result.issues.push(`Group ${exportedGroup.id}: name mismatch between systems`)
            result.passed = false
          }

          if (staffGroup.teacherId !== studentGroupRef.teacherReferenceId) {
            result.issues.push(`Group ${exportedGroup.id}: teacher reference mismatch`)
            result.passed = false
          }
        }
      }

      result.recordCount.missing = result.recordCount.expected - result.recordCount.actual

      if (result.recordCount.missing > 0) {
        result.recommendations.push(`${result.recordCount.missing} groups are missing from the staff database`)
      }

    } catch (error) {
      result.issues.push(`Validation error: ${error}`)
      result.passed = false
    }

    return result
  }

  async validateTeachers(exportedTeachers: any[]): Promise<ValidationResult> {
    const result: ValidationResult = {
      table: 'teachers',
      passed: true,
      issues: [],
      recommendations: [],
      recordCount: {
        expected: exportedTeachers.length,
        actual: 0,
        missing: 0
      }
    }

    try {
      // Check staff database teachers
      // TODO: Implement actual Prisma queries
      // const staffTeachers = await this.staffPrisma.teacher.findMany()
      const staffTeachers: any[] = [] // Mock for now
      
      // Check students server teacher references
      const teacherRefsResponse = await this.studentsClient.getTeacherReferences()
      const studentTeacherRefs = teacherRefsResponse.data || []

      result.recordCount.actual = staffTeachers.length

      // Validate teachers exist in both systems
      for (const exportedTeacher of exportedTeachers) {
        const staffTeacher = staffTeachers.find(t => t.id === exportedTeacher.id)
        const studentTeacherRef = studentTeacherRefs.find(t => t.id === exportedTeacher.id)

        if (!staffTeacher) {
          result.issues.push(`Teacher ${exportedTeacher.id} (${exportedTeacher.name}) not found in staff database`)
          result.passed = false
        }

        if (!studentTeacherRef) {
          result.issues.push(`Teacher ${exportedTeacher.id} (${exportedTeacher.name}) not found in students database`)
          result.passed = false
        }

        // Validate consistency
        if (staffTeacher && studentTeacherRef) {
          if (staffTeacher.name !== studentTeacherRef.name) {
            result.issues.push(`Teacher ${exportedTeacher.id}: name mismatch between systems`)
            result.passed = false
          }

          if (staffTeacher.subject !== studentTeacherRef.subject) {
            result.issues.push(`Teacher ${exportedTeacher.id}: subject mismatch between systems`)
            result.passed = false
          }
        }
      }

      result.recordCount.missing = result.recordCount.expected - result.recordCount.actual

    } catch (error) {
      result.issues.push(`Validation error: ${error}`)
      result.passed = false
    }

    return result
  }

  async validatePayments(exportedPayments: any[]): Promise<ValidationResult> {
    const result: ValidationResult = {
      table: 'payments',
      passed: true,
      issues: [],
      recommendations: [],
      recordCount: {
        expected: exportedPayments.length,
        actual: 0,
        missing: 0
      }
    }

    try {
      // Check staff database payment overviews
      // TODO: Implement actual Prisma queries
      // const staffPayments = await this.staffPrisma.paymentOverview.findMany()
      const staffPayments: any[] = [] // Mock for now

      result.recordCount.actual = staffPayments.length

      // Validate payment records
      for (const exportedPayment of exportedPayments) {
        const staffPayment = staffPayments.find(p => p.id === exportedPayment.id)

        if (!staffPayment) {
          result.issues.push(`Payment ${exportedPayment.id} not found in staff database`)
          result.passed = false
          continue
        }

        // Validate amounts match
        if (staffPayment.amount !== exportedPayment.amount) {
          result.issues.push(`Payment ${exportedPayment.id}: amount mismatch`)
          result.passed = false
        }

        // Validate status consistency
        if (staffPayment.status !== exportedPayment.status) {
          result.issues.push(`Payment ${exportedPayment.id}: status mismatch`)
          result.passed = false
        }
      }

      result.recordCount.missing = result.recordCount.expected - result.recordCount.actual

    } catch (error) {
      result.issues.push(`Validation error: ${error}`)
      result.passed = false
    }

    return result
  }

  async validateInterServerCommunication(): Promise<ValidationResult> {
    const result: ValidationResult = {
      table: 'inter-server-communication',
      passed: true,
      issues: [],
      recommendations: [],
      recordCount: { expected: 0, actual: 0, missing: 0 }
    }

    try {
      // Test health check
      const healthResponse = await this.studentsClient.healthCheck()
      if (!healthResponse.success) {
        result.issues.push('Students server health check failed')
        result.passed = false
      }

      // Test API endpoints
      const groupsResponse = await this.studentsClient.getGroupReferences()
      if (!groupsResponse.success) {
        result.issues.push('Failed to fetch group references from students server')
        result.passed = false
      }

      const teachersResponse = await this.studentsClient.getTeacherReferences()
      if (!teachersResponse.success) {
        result.issues.push('Failed to fetch teacher references from students server')
        result.passed = false
      }

      if (result.passed) {
        result.recommendations.push('Inter-server communication is working correctly')
      }

    } catch (error) {
      result.issues.push(`Inter-server communication error: ${error}`)
      result.passed = false
    }

    return result
  }

  async runValidation(): Promise<ValidationSummary> {
    const startTime = Date.now()
    
    console.log('Starting migration validation...')
    
    try {
      const exportedData = await this.loadExportedData()
      
      const results = [
        await this.validateStudents(exportedData.students),
        await this.validateGroups(exportedData.groups),
        await this.validateTeachers(exportedData.teachers),
        await this.validatePayments(exportedData.payments),
        await this.validateInterServerCommunication()
      ]

      const validationTime = Date.now() - startTime
      const totalIssues = results.reduce((sum, result) => sum + result.issues.length, 0)
      const overallPassed = results.every(result => result.passed)

      const summary: ValidationSummary = {
        overall: {
          passed: overallPassed,
          totalIssues,
          validationTime
        },
        results
      }

      // Save validation report
      await fs.writeFile(
        path.join(this.exportDir, 'validation-report.json'),
        JSON.stringify(summary, null, 2)
      )

      return summary
    } catch (error) {
      throw new Error(`Validation failed: ${error}`)
    } finally {
      await this.staffPrisma.$disconnect()
    }
  }
}

// Main execution
async function main() {
  const validator = new MigrationValidator()
  
  try {
    const summary = await validator.runValidation()
    
    console.log('\n=== Validation Summary ===')
    
    summary.results.forEach(result => {
      const status = result.passed ? '✅ PASSED' : '❌ FAILED'
      console.log(`${result.table}: ${status}`)
      
      if (result.recordCount.expected > 0) {
        console.log(`  Records: ${result.recordCount.actual}/${result.recordCount.expected} (${result.recordCount.missing} missing)`)
      }
      
      if (result.issues.length > 0) {
        console.log(`  Issues: ${result.issues.length}`)
        result.issues.forEach(issue => console.log(`    - ${issue}`))
      }
      
      if (result.recommendations.length > 0) {
        result.recommendations.forEach(rec => console.log(`    💡 ${rec}`))
      }
      
      console.log()
    })
    
    console.log(`Overall: ${summary.overall.passed ? '✅ PASSED' : '❌ FAILED'}`)
    console.log(`Total Issues: ${summary.overall.totalIssues}`)
    console.log(`Validation Time: ${summary.overall.validationTime}ms`)
    
    if (!summary.overall.passed) {
      console.log('\n⚠️  Migration validation failed. Please review the issues above.')
      process.exit(1)
    } else {
      console.log('\n🎉 Migration validation passed successfully!')
      process.exit(0)
    }
  } catch (error) {
    console.error('Validation failed:', error)
    process.exit(1)
  }
}

if (require.main === module) {
  main()
}
