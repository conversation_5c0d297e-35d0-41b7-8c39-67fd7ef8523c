"use client"

import { useEffect, useState } from 'react'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { Button } from '@/components/ui/button'
import { Shield, ShieldCheck, ShieldX, RefreshCw, AlertTriangle, Info } from 'lucide-react'
import { Alert, AlertDescription, AlertTitle } from '@/components/ui/alert'

interface VPNStatus {
  connected: boolean
  clientIP: string
  allowedRange?: string
  vpnRequired: boolean
  lastChecked: string
}

export function VPNStatus() {
  const [status, setStatus] = useState<VPNStatus | null>(null)
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)

  const checkVPNStatus = async () => {
    setLoading(true)
    setError(null)

    try {
      const response = await fetch('/api/security/vpn-status')
      const data = await response.json()

      if (!response.ok) {
        throw new Error(data.error || 'Failed to check VPN status')
      }

      setStatus(data)
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Unknown error')
    } finally {
      setLoading(false)
    }
  }

  useEffect(() => {
    checkVPNStatus()
    
    // Check VPN status every 30 seconds
    const interval = setInterval(checkVPNStatus, 30000)
    
    return () => clearInterval(interval)
  }, [])

  if (loading && !status) {
    return (
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center space-x-2">
            <Shield className="h-5 w-5" />
            <span>VPN Status</span>
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="flex items-center space-x-2">
            <RefreshCw className="h-4 w-4 animate-spin" />
            <span>Checking VPN status...</span>
          </div>
        </CardContent>
      </Card>
    )
  }

  if (error) {
    return (
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center space-x-2">
            <ShieldX className="h-5 w-5 text-red-500" />
            <span>VPN Status</span>
          </CardTitle>
        </CardHeader>
        <CardContent>
          <Alert variant="destructive">
            <AlertTriangle className="h-4 w-4" />
            <AlertTitle>Error</AlertTitle>
            <AlertDescription>{error}</AlertDescription>
          </Alert>
          <Button 
            onClick={checkVPNStatus} 
            variant="outline" 
            size="sm" 
            className="mt-3"
            disabled={loading}
          >
            <RefreshCw className={`h-4 w-4 mr-2 ${loading ? 'animate-spin' : ''}`} />
            Retry
          </Button>
        </CardContent>
      </Card>
    )
  }

  if (!status) {
    return null
  }

  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center justify-between">
          <div className="flex items-center space-x-2">
            {status.connected ? (
              <ShieldCheck className="h-5 w-5 text-green-500" />
            ) : (
              <ShieldX className="h-5 w-5 text-red-500" />
            )}
            <span>VPN Status</span>
          </div>
          <Button 
            onClick={checkVPNStatus} 
            variant="ghost" 
            size="sm"
            disabled={loading}
          >
            <RefreshCw className={`h-4 w-4 ${loading ? 'animate-spin' : ''}`} />
          </Button>
        </CardTitle>
        <CardDescription>
          Security connection status
        </CardDescription>
      </CardHeader>
      <CardContent className="space-y-4">
        {/* Connection Status */}
        <div className="flex items-center justify-between">
          <span className="text-sm font-medium">Connection:</span>
          <Badge variant={status.connected ? "default" : "destructive"}>
            {status.connected ? "Connected" : "Not Connected"}
          </Badge>
        </div>

        {/* Client IP */}
        <div className="flex items-center justify-between">
          <span className="text-sm font-medium">Your IP:</span>
          <code className="text-sm bg-gray-100 px-2 py-1 rounded">
            {status.clientIP}
          </code>
        </div>

        {/* Allowed Range */}
        {status.allowedRange && (
          <div className="flex items-center justify-between">
            <span className="text-sm font-medium">Allowed Range:</span>
            <code className="text-sm bg-gray-100 px-2 py-1 rounded">
              {status.allowedRange}
            </code>
          </div>
        )}

        {/* VPN Required Status */}
        <div className="flex items-center justify-between">
          <span className="text-sm font-medium">VPN Required:</span>
          <Badge variant={status.vpnRequired ? "secondary" : "outline"}>
            {status.vpnRequired ? "Yes" : "No"}
          </Badge>
        </div>

        {/* Last Checked */}
        <div className="flex items-center justify-between">
          <span className="text-sm font-medium">Last Checked:</span>
          <span className="text-sm text-gray-500">
            {new Date(status.lastChecked).toLocaleTimeString()}
          </span>
        </div>

        {/* Warning for non-VPN access */}
        {status.vpnRequired && !status.connected && (
          <Alert variant="destructive">
            <AlertTriangle className="h-4 w-4" />
            <AlertTitle>VPN Required</AlertTitle>
            <AlertDescription>
              This application requires VPN access. Please connect to the authorized VPN network.
            </AlertDescription>
          </Alert>
        )}

        {/* Info for VPN bypass in development */}
        {!status.vpnRequired && process.env.NODE_ENV === 'development' && (
          <Alert>
            <Info className="h-4 w-4" />
            <AlertTitle>Development Mode</AlertTitle>
            <AlertDescription>
              VPN requirement is bypassed in development mode.
            </AlertDescription>
          </Alert>
        )}

        {/* Success message */}
        {status.connected && status.vpnRequired && (
          <Alert className="border-green-200 bg-green-50">
            <ShieldCheck className="h-4 w-4 text-green-600" />
            <AlertTitle className="text-green-800">Secure Connection</AlertTitle>
            <AlertDescription className="text-green-700">
              You are connected through an authorized VPN network.
            </AlertDescription>
          </Alert>
        )}
      </CardContent>
    </Card>
  )
}
