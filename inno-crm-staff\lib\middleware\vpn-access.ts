import { NextRequest, NextResponse } from 'next/server'
import { headers } from 'next/headers'

interface VPNConfig {
  enabled: boolean
  allowedRanges: string[]
  bypassPaths: string[]
  developmentBypass: boolean
}

interface IPRange {
  network: string
  mask: number
}

export class VPNAccessControl {
  private config: VPNConfig

  constructor(config: VPNConfig) {
    this.config = config
  }

  // Parse CIDR notation (e.g., "***********/24")
  private parseCIDR(cidr: string): IPRange {
    const [network, maskStr] = cidr.split('/')
    const mask = parseInt(maskStr, 10)
    return { network, mask }
  }

  // Convert IP address to 32-bit integer
  private ipToInt(ip: string): number {
    return ip.split('.').reduce((acc, octet) => (acc << 8) + parseInt(octet, 10), 0) >>> 0
  }

  // Check if IP is in CIDR range
  private isIPInRange(ip: string, range: IPRange): boolean {
    try {
      const ipInt = this.ipToInt(ip)
      const networkInt = this.ipToInt(range.network)
      const mask = (0xffffffff << (32 - range.mask)) >>> 0
      
      return (ipInt & mask) === (networkInt & mask)
    } catch (error) {
      console.error('Error checking IP range:', error)
      return false
    }
  }

  // Extract client IP from request
  private getClientIP(request: NextRequest): string {
    // Check various headers for the real client IP
    const forwardedFor = request.headers.get('x-forwarded-for')
    const realIP = request.headers.get('x-real-ip')
    const cfConnectingIP = request.headers.get('cf-connecting-ip') // Cloudflare
    const xClientIP = request.headers.get('x-client-ip')
    
    // x-forwarded-for can contain multiple IPs, take the first one
    if (forwardedFor) {
      const ips = forwardedFor.split(',').map(ip => ip.trim())
      return ips[0]
    }
    
    if (realIP) return realIP
    if (cfConnectingIP) return cfConnectingIP
    if (xClientIP) return xClientIP
    
    // Fallback to connection IP (may not be accurate behind proxies)
    return request.ip || 'unknown'
  }

  // Validate IP address format
  private isValidIP(ip: string): boolean {
    const ipRegex = /^(?:(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\.){3}(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)$/
    return ipRegex.test(ip)
  }

  // Check if path should bypass VPN check
  private shouldBypassPath(pathname: string): boolean {
    return this.config.bypassPaths.some(path => {
      if (path.endsWith('*')) {
        return pathname.startsWith(path.slice(0, -1))
      }
      return pathname === path
    })
  }

  // Main access control check
  checkAccess(request: NextRequest): {
    allowed: boolean
    reason?: string
    clientIP?: string
    matchedRange?: string
  } {
    const pathname = request.nextUrl.pathname

    // Skip VPN check if disabled
    if (!this.config.enabled) {
      return { allowed: true, reason: 'VPN check disabled' }
    }

    // Skip VPN check in development if bypass is enabled
    if (this.config.developmentBypass && process.env.NODE_ENV === 'development') {
      return { allowed: true, reason: 'Development bypass enabled' }
    }

    // Skip VPN check for bypass paths
    if (this.shouldBypassPath(pathname)) {
      return { allowed: true, reason: `Path ${pathname} bypasses VPN check` }
    }

    const clientIP = this.getClientIP(request)

    // Validate IP format
    if (!this.isValidIP(clientIP)) {
      return {
        allowed: false,
        reason: `Invalid IP format: ${clientIP}`,
        clientIP
      }
    }

    // Check if IP is in allowed ranges
    for (const rangeStr of this.config.allowedRanges) {
      try {
        const range = this.parseCIDR(rangeStr)
        if (this.isIPInRange(clientIP, range)) {
          return {
            allowed: true,
            reason: `IP ${clientIP} matches allowed range ${rangeStr}`,
            clientIP,
            matchedRange: rangeStr
          }
        }
      } catch (error) {
        console.error(`Error parsing CIDR range ${rangeStr}:`, error)
      }
    }

    return {
      allowed: false,
      reason: `IP ${clientIP} not in allowed VPN ranges`,
      clientIP
    }
  }

  // Create middleware function
  createMiddleware() {
    return (request: NextRequest): NextResponse | null => {
      const result = this.checkAccess(request)

      if (!result.allowed) {
        console.warn(`VPN Access Denied: ${result.reason}`)
        
        // Log security event
        this.logSecurityEvent({
          type: 'VPN_ACCESS_DENIED',
          clientIP: result.clientIP,
          reason: result.reason,
          path: request.nextUrl.pathname,
          userAgent: request.headers.get('user-agent'),
          timestamp: new Date().toISOString()
        })

        // Return access denied response
        return NextResponse.json(
          {
            error: 'Access Denied',
            message: 'This application requires VPN access. Please connect to the authorized VPN and try again.',
            code: 'VPN_REQUIRED',
            timestamp: new Date().toISOString()
          },
          { 
            status: 403,
            headers: {
              'X-Access-Denied-Reason': 'VPN_REQUIRED',
              'X-Client-IP': result.clientIP || 'unknown'
            }
          }
        )
      }

      // Log successful access (optional, for audit)
      if (result.matchedRange) {
        console.log(`VPN Access Granted: ${result.reason}`)
      }

      return null // Allow request to continue
    }
  }

  // Log security events
  private logSecurityEvent(event: {
    type: string
    clientIP?: string
    reason?: string
    path: string
    userAgent?: string | null
    timestamp: string
  }) {
    // TODO: Implement proper security logging
    // This could write to a security log file, send to a SIEM system, etc.
    console.warn('SECURITY EVENT:', JSON.stringify(event, null, 2))
    
    // In production, you might want to:
    // - Write to a dedicated security log file
    // - Send alerts for repeated access attempts
    // - Store in a security events database
    // - Integrate with monitoring systems
  }

  // Get access statistics
  getAccessStats(): {
    totalAttempts: number
    allowedAttempts: number
    deniedAttempts: number
    topDeniedIPs: string[]
  } {
    // TODO: Implement access statistics tracking
    // This would require storing access attempts in memory or database
    return {
      totalAttempts: 0,
      allowedAttempts: 0,
      deniedAttempts: 0,
      topDeniedIPs: []
    }
  }
}

// Create VPN access control instance
export function createVPNAccessControl(): VPNAccessControl {
  const config: VPNConfig = {
    enabled: process.env.VPN_REQUIRED === 'true',
    allowedRanges: (process.env.ALLOWED_IP_RANGES || '').split(',').filter(Boolean),
    bypassPaths: [
      '/api/health',
      '/api/auth/signin',
      '/auth/signin',
      '/auth/error',
      '/_next/*',
      '/favicon.ico',
      '/manifest.json',
      '/sw.js',
      '/icons/*'
    ],
    developmentBypass: process.env.NODE_ENV === 'development'
  }

  return new VPNAccessControl(config)
}

// Utility function to test IP ranges
export function testIPRange(ip: string, cidr: string): boolean {
  try {
    const vpn = createVPNAccessControl()
    const range = vpn['parseCIDR'](cidr)
    return vpn['isIPInRange'](ip, range)
  } catch (error) {
    console.error('Error testing IP range:', error)
    return false
  }
}
